#ifndef PHOTO_GALLERY_BRIDGE_H
#define PHOTO_GALLERY_BRIDGE_H

#include <stdbool.h>
#include <stdint.h>
#include <stdlib.h>

#ifdef __cplusplus
extern "C" {
#endif

// 相片资源结构体
typedef struct {
    char* identifier;
    int64_t creation_date;
    int64_t modification_date;
    uint32_t media_type;
    bool has_location;
    double latitude;
    double longitude;
} PhotoAsset;

// 相片信息结构体
typedef struct {
    char* file_name;
    int64_t file_size;
    uint32_t width;
    uint32_t height;
    uint32_t format;
    int64_t creation_date;
} PhotoInfo;

// 缩略图数据结构体
typedef struct {
    uint8_t* data;
    size_t length;
    uint32_t width;
    uint32_t height;
} ThumbnailData;

// 相片资源数组
typedef struct {
    PhotoAsset* assets;
    size_t count;
} PhotoAssetArray;

// 媒体类型枚举
typedef enum {
    MediaTypeUnknown = 0,
    MediaTypeImage = 1,
    MediaTypeVideo = 2,
    MediaTypeAudio = 3
} MediaType;

// 图像格式枚举
typedef enum {
    ImageFormatUnknown = 0,
    ImageFormatJPEG = 1,
    ImageFormatPNG = 2,
    ImageFormatHEIC = 3,
    ImageFormatRAW = 4
} ImageFormat;

// 错误代码枚举
typedef enum {
    PhotoGalleryErrorNone = 0,
    PhotoGalleryErrorPermissionDenied = 1,
    PhotoGalleryErrorAssetNotFound = 2,
    PhotoGalleryErrorInvalidFormat = 3,
    PhotoGalleryErrorMemoryError = 4,
    PhotoGalleryErrorUnknown = 5
} PhotoGalleryError;

// FFI 函数声明
bool request_photo_gallery_permission(void);
PhotoAssetArray* fetch_photo_assets(void);
ThumbnailData* generate_thumbnail(const char* asset_id, uint32_t width, uint32_t height);
PhotoInfo* get_photo_info(const char* asset_id);
void free_photo_asset_array(PhotoAssetArray* array);
void free_thumbnail_data(ThumbnailData* data);
void free_photo_info(PhotoInfo* info);
const char* get_last_error_message(void);
int32_t get_last_error_code(void);

// 强制导出这些函数
#define EXPORT __attribute__((visibility("default")))

EXPORT bool request_photo_gallery_permission(void);
EXPORT PhotoAssetArray* fetch_photo_assets(void);
EXPORT ThumbnailData* generate_thumbnail(const char* asset_id, uint32_t width, uint32_t height);
EXPORT PhotoInfo* get_photo_info(const char* asset_id);
EXPORT void free_photo_asset_array(PhotoAssetArray* array);
EXPORT void free_thumbnail_data(ThumbnailData* data);
EXPORT void free_photo_info(PhotoInfo* info);
EXPORT const char* get_last_error_message(void);
EXPORT int32_t get_last_error_code(void);

#ifdef __cplusplus
}
#endif

#endif // PHOTO_GALLERY_BRIDGE_H