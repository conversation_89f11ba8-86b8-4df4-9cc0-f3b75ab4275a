---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/libPhotoGalleryBridge.dylib'
relocations:
  - { offset: 0x10E202, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge13lastErrorInfo33_3AE31ED88418F257D5A36F2DDFC489ADLLAA0eF0VSgvp', symObjAddr: 0x19140, symBinAddr: 0xC910, symSize: 0x0 }
  - { offset: 0x10E217, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0A11AssetStructV10identifierSPys4Int8VGSgvpfi', symObjAddr: 0x0, symBinAddr: 0x20E4, symSize: 0x8 }
  - { offset: 0x10E22F, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0A10InfoStructV9file_nameSPys4Int8VGSgvpfi', symObjAddr: 0x8, symBinAddr: 0x20EC, symSize: 0x8 }
  - { offset: 0x10E247, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge19ThumbnailDataStructV4dataSPys5UInt8VGSgvpfi', symObjAddr: 0x10, symBinAddr: 0x20F4, symSize: 0x8 }
  - { offset: 0x10E25F, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0A10AssetArrayV6assetsSpyAA0aD6StructVGSgvpfi', symObjAddr: 0x18, symBinAddr: 0x20FC, symSize: 0x8 }
  - { offset: 0x10E277, size: 0x8, addend: 0x0, symName: '_$sSo21PHAuthorizationStatusVIegy_ABIeyBy_TR', symObjAddr: 0x20, symBinAddr: 0x2104, symSize: 0x3C }
  - { offset: 0x10E28F, size: 0x8, addend: 0x0, symName: '_$sSo7PHAssetCSiSpy10ObjectiveC8ObjCBoolVGIeggyy_ABSiAFIeyByyy_TR', symObjAddr: 0x5C, symBinAddr: 0x2140, symSize: 0x68 }
  - { offset: 0x10E2A7, size: 0x8, addend: 0x0, symName: '_$sSo7NSImageCSgSDys11AnyHashableVypGSgIeggg_ACSo12NSDictionaryCSgIeyByy_TR', symObjAddr: 0xC4, symBinAddr: 0x21A8, symSize: 0x9C }
  - { offset: 0x10E43F, size: 0x8, addend: 0x0, symName: _request_photo_gallery_permission, symObjAddr: 0x170, symBinAddr: 0x2254, symSize: 0x4 }
  - { offset: 0x10E462, size: 0x8, addend: 0x0, symName: _request_photo_gallery_permission, symObjAddr: 0x170, symBinAddr: 0x2254, symSize: 0x4 }
  - { offset: 0x10E50F, size: 0x8, addend: 0x0, symName: _fetch_photo_assets, symObjAddr: 0x174, symBinAddr: 0x2258, symSize: 0x61C }
  - { offset: 0x10EBF0, size: 0x8, addend: 0x0, symName: _generate_thumbnail, symObjAddr: 0x790, symBinAddr: 0x2874, symSize: 0x2DC }
  - { offset: 0x10EEFA, size: 0x8, addend: 0x0, symName: _get_photo_info, symObjAddr: 0xA6C, symBinAddr: 0x2B50, symSize: 0x30C }
  - { offset: 0x10F1EB, size: 0x8, addend: 0x0, symName: _free_photo_asset_array, symObjAddr: 0xD78, symBinAddr: 0x2E5C, symSize: 0x8C }
  - { offset: 0x10F34A, size: 0x8, addend: 0x0, symName: _get_last_error_message, symObjAddr: 0xE50, symBinAddr: 0x2F34, symSize: 0x15C }
  - { offset: 0x10F5F3, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge19getLastErrorMessageSPys4Int8VGSgyFAfEXEfU_', symObjAddr: 0xFAC, symBinAddr: 0x3090, symSize: 0x6C }
  - { offset: 0x10F6DA, size: 0x8, addend: 0x0, symName: _get_last_error_code, symObjAddr: 0x1018, symBinAddr: 0x30FC, symSize: 0x1C }
  - { offset: 0x10F7AB, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlFSo7PHAssetC_Ttg5', symObjAddr: 0x13EC, symBinAddr: 0x34D0, symSize: 0x6C }
  - { offset: 0x10F7C3, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlFSo16NSSortDescriptorC_Ttg5', symObjAddr: 0x1458, symBinAddr: 0x353C, symSize: 0x6C }
  - { offset: 0x10F7F1, size: 0x8, addend: 0x0, symName: '_$sSa034_makeUniqueAndReserveCapacityIfNotB0yyFSo7PHAssetC_Tg5', symObjAddr: 0x14C4, symBinAddr: 0x35A8, symSize: 0x70 }
  - { offset: 0x10F8CE, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSo7PHAssetC_Tg5', symObjAddr: 0x1534, symBinAddr: 0x3618, symSize: 0x128 }
  - { offset: 0x10F9FC, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfCSo7PHAssetC_Tt1g5', symObjAddr: 0x165C, symBinAddr: 0x3740, symSize: 0x80 }
  - { offset: 0x10FA55, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV13_copyContents8subRange12initializingSpyxGSnySiG_AFtFSo7PHAssetC_Tg5', symObjAddr: 0x16DC, symBinAddr: 0x37C0, symSize: 0x118 }
  - { offset: 0x10FB35, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSo27NSBitmapImageRepPropertyKeya_Tg5', symObjAddr: 0x17F4, symBinAddr: 0x38D8, symSize: 0x80 }
  - { offset: 0x10FBBE, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSo27NSBitmapImageRepPropertyKeya_Tg5', symObjAddr: 0x1874, symBinAddr: 0x3958, symSize: 0x178 }
  - { offset: 0x10FCAE, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV19_getElementSlowPathyyXlSiFSo15PHAssetResourceC_Tg5', symObjAddr: 0x19EC, symBinAddr: 0x3AD0, symSize: 0x1D4 }
  - { offset: 0x10FD2F, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV19_getElementSlowPathyyXlSiFSo7PHAssetC_Tg5', symObjAddr: 0x1BC0, symBinAddr: 0x3CA4, symSize: 0x1D4 }
  - { offset: 0x10FDB0, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledName, symObjAddr: 0x2108, symBinAddr: 0x41EC, symSize: 0x44 }
  - { offset: 0x10FDE5, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DateVSgWOb', symObjAddr: 0x2424, symBinAddr: 0x4508, symSize: 0x48 }
  - { offset: 0x10FDF9, size: 0x8, addend: 0x0, symName: '_$sS2SSysWl', symObjAddr: 0x246C, symBinAddr: 0x4550, symSize: 0x40 }
  - { offset: 0x10FE0D, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge9ErrorInfoVSgWOe', symObjAddr: 0x24AC, symBinAddr: 0x4590, symSize: 0x30 }
  - { offset: 0x10FEB0, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVSgWOe', symObjAddr: 0x2E24, symBinAddr: 0x4F08, symSize: 0x14 }
  - { offset: 0x10FEC4, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOe', symObjAddr: 0x2E38, symBinAddr: 0x4F1C, symSize: 0x40 }
  - { offset: 0x10FED8, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0A8InfoDataVSgWOs', symObjAddr: 0x2E78, symBinAddr: 0x4F5C, symSize: 0x28 }
  - { offset: 0x10FEEC, size: 0x8, addend: 0x0, symName: ___swift_noop_void_return, symObjAddr: 0x2EA0, symBinAddr: 0x4F84, symSize: 0x4 }
  - { offset: 0x10FF00, size: 0x8, addend: 0x0, symName: ___swift_memcpy48_8, symObjAddr: 0x2EA4, symBinAddr: 0x4F88, symSize: 0x14 }
  - { offset: 0x10FF14, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0A11AssetStructVwet', symObjAddr: 0x2EB8, symBinAddr: 0x4F9C, symSize: 0x54 }
  - { offset: 0x10FF28, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0A11AssetStructVwst', symObjAddr: 0x2F0C, symBinAddr: 0x4FF0, symSize: 0x50 }
  - { offset: 0x10FF3C, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0A11AssetStructVMa', symObjAddr: 0x2F5C, symBinAddr: 0x5040, symSize: 0x10 }
  - { offset: 0x10FF50, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0A10InfoStructVwCP', symObjAddr: 0x2F6C, symBinAddr: 0x5050, symSize: 0x2C }
  - { offset: 0x10FF64, size: 0x8, addend: 0x0, symName: ___swift_memcpy40_8, symObjAddr: 0x2F98, symBinAddr: 0x507C, symSize: 0x14 }
  - { offset: 0x10FF78, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0A10InfoStructVwet', symObjAddr: 0x2FAC, symBinAddr: 0x5090, symSize: 0x20 }
  - { offset: 0x10FF8C, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0A10InfoStructVwst', symObjAddr: 0x2FCC, symBinAddr: 0x50B0, symSize: 0x34 }
  - { offset: 0x10FFA0, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0A10InfoStructVMa', symObjAddr: 0x3000, symBinAddr: 0x50E4, symSize: 0x10 }
  - { offset: 0x10FFB4, size: 0x8, addend: 0x0, symName: ___swift_memcpy24_8, symObjAddr: 0x3010, symBinAddr: 0x50F4, symSize: 0x14 }
  - { offset: 0x10FFC8, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge19ThumbnailDataStructVwet', symObjAddr: 0x3024, symBinAddr: 0x5108, symSize: 0x20 }
  - { offset: 0x10FFDC, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge19ThumbnailDataStructVwst', symObjAddr: 0x3044, symBinAddr: 0x5128, symSize: 0x2C }
  - { offset: 0x10FFF0, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge19ThumbnailDataStructVMa', symObjAddr: 0x3070, symBinAddr: 0x5154, symSize: 0x10 }
  - { offset: 0x110004, size: 0x8, addend: 0x0, symName: ___swift_memcpy16_8, symObjAddr: 0x3080, symBinAddr: 0x5164, symSize: 0xC }
  - { offset: 0x110018, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0A10AssetArrayVwet', symObjAddr: 0x308C, symBinAddr: 0x5170, symSize: 0x20 }
  - { offset: 0x11002C, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0A10AssetArrayVwst', symObjAddr: 0x30AC, symBinAddr: 0x5190, symSize: 0x28 }
  - { offset: 0x110040, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0A10AssetArrayVMa', symObjAddr: 0x30D4, symBinAddr: 0x51B8, symSize: 0x10 }
  - { offset: 0x110054, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0aB7ManagerCMa', symObjAddr: 0x30E4, symBinAddr: 0x51C8, symSize: 0x20 }
  - { offset: 0x1100AA, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0aB7ManagerC17generateThumbnail3for10targetSize10Foundation4DataVSgSo7PHAssetC_So6CGSizeVtFySo7NSImageCSg_SDys11AnyHashableVypGSgtcfU_TA', symObjAddr: 0x3138, symBinAddr: 0x521C, symSize: 0x26C }
  - { offset: 0x1101BC, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x33A4, symBinAddr: 0x5488, symSize: 0x10 }
  - { offset: 0x1101D0, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x33B4, symBinAddr: 0x5498, symSize: 0x8 }
  - { offset: 0x1101E4, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVSgWOy', symObjAddr: 0x33BC, symBinAddr: 0x54A0, symSize: 0x14 }
  - { offset: 0x1101F8, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOy', symObjAddr: 0x33D0, symBinAddr: 0x54B4, symSize: 0x40 }
  - { offset: 0x11027A, size: 0x8, addend: 0x0, symName: '_$sSo27NSBitmapImageRepPropertyKeyaMa', symObjAddr: 0x353C, symBinAddr: 0x5620, symSize: 0x50 }
  - { offset: 0x11028E, size: 0x8, addend: 0x0, symName: '_$sSo27NSBitmapImageRepPropertyKeya_yptWOc', symObjAddr: 0x358C, symBinAddr: 0x5670, symSize: 0x48 }
  - { offset: 0x1102A2, size: 0x8, addend: 0x0, symName: '_$sypWOb', symObjAddr: 0x35D4, symBinAddr: 0x56B8, symSize: 0x10 }
  - { offset: 0x1102E2, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0aB7ManagerC05fetchA6AssetsSaySo7PHAssetCGyFyAF_SiSpy10ObjectiveC8ObjCBoolVGtcfU_TA', symObjAddr: 0x3648, symBinAddr: 0x572C, symSize: 0xA8 }
  - { offset: 0x1103C3, size: 0x8, addend: 0x0, symName: '_$sSo27NSBitmapImageRepPropertyKeyas20_SwiftNewtypeWrapperSCSYWb', symObjAddr: 0x380C, symBinAddr: 0x58F0, symSize: 0x24 }
  - { offset: 0x1103D7, size: 0x8, addend: 0x0, symName: '_$sSo27NSBitmapImageRepPropertyKeyas20_SwiftNewtypeWrapperSCs35_HasCustomAnyHashableRepresentationPWb', symObjAddr: 0x3830, symBinAddr: 0x5914, symSize: 0x24 }
  - { offset: 0x1103EB, size: 0x8, addend: 0x0, symName: '_$sSo27NSBitmapImageRepPropertyKeyaSHSCSQWb', symObjAddr: 0x3854, symBinAddr: 0x5938, symSize: 0x24 }
  - { offset: 0x1106AF, size: 0x8, addend: 0x0, symName: '_$sSo27NSBitmapImageRepPropertyKeyas21_ObjectiveCBridgeableSCsACP016_forceBridgeFromF1C_6resulty01_F5CTypeQz_xSgztFZTW', symObjAddr: 0x1040, symBinAddr: 0x3124, symSize: 0x4 }
  - { offset: 0x1106D2, size: 0x8, addend: 0x0, symName: '_$ss20_SwiftNewtypeWrapperPss21_ObjectiveCBridgeable8RawValueRpzrlE016_forceBridgeFromD1C_6resultyAD_01_D5CTypeQZ_xSgztFZSo27NSBitmapImageRepPropertyKeya_Tt1gq5', symObjAddr: 0x1044, symBinAddr: 0x3128, symSize: 0x84 }
  - { offset: 0x110764, size: 0x8, addend: 0x0, symName: '_$sSo27NSBitmapImageRepPropertyKeyas21_ObjectiveCBridgeableSCsACP024_conditionallyBridgeFromF1C_6resultSb01_F5CTypeQz_xSgztFZTW', symObjAddr: 0x10C8, symBinAddr: 0x31AC, symSize: 0x4 }
  - { offset: 0x110780, size: 0x8, addend: 0x0, symName: '_$ss20_SwiftNewtypeWrapperPss21_ObjectiveCBridgeable8RawValueRpzrlE024_conditionallyBridgeFromD1C_6resultSbAD_01_D5CTypeQZ_xSgztFZSo27NSBitmapImageRepPropertyKeya_Tt1gq5', symObjAddr: 0x10CC, symBinAddr: 0x31B0, symSize: 0x8C }
  - { offset: 0x110821, size: 0x8, addend: 0x0, symName: '_$sSo27NSBitmapImageRepPropertyKeyas21_ObjectiveCBridgeableSCsACP026_unconditionallyBridgeFromF1Cyx01_F5CTypeQzSgFZTW', symObjAddr: 0x1158, symBinAddr: 0x323C, symSize: 0x40 }
  - { offset: 0x1108A0, size: 0x8, addend: 0x0, symName: '_$sSo27NSBitmapImageRepPropertyKeyaSHSCSH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x11E0, symBinAddr: 0x32C4, symSize: 0x40 }
  - { offset: 0x11091E, size: 0x8, addend: 0x0, symName: '_$sSo27NSBitmapImageRepPropertyKeyaSHSCSH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x1220, symBinAddr: 0x3304, symSize: 0x70 }
  - { offset: 0x110994, size: 0x8, addend: 0x0, symName: '_$sSo27NSBitmapImageRepPropertyKeyaSQSCSQ2eeoiySbx_xtFZTW', symObjAddr: 0x1290, symBinAddr: 0x3374, symSize: 0x84 }
  - { offset: 0x110A36, size: 0x8, addend: 0x0, symName: '_$sSo27NSBitmapImageRepPropertyKeyas35_HasCustomAnyHashableRepresentationSCsACP03_toghI0s0hI0VSgyFTW', symObjAddr: 0x1380, symBinAddr: 0x3464, symSize: 0x6C }
  - { offset: 0x110BB3, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSo27NSBitmapImageRepPropertyKeya_ypTt0g5Tf4g_n', symObjAddr: 0x3410, symBinAddr: 0x54F4, symSize: 0xF0 }
  - { offset: 0x110E80, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0aB7ManagerCfD', symObjAddr: 0x160, symBinAddr: 0x2244, symSize: 0x10 }
  - { offset: 0x1111B9, size: 0x8, addend: 0x0, symName: '_$sSo27NSBitmapImageRepPropertyKeyaSYSCSY8rawValuexSg03RawG0Qz_tcfCTW', symObjAddr: 0x1314, symBinAddr: 0x33F8, symSize: 0x44 }
  - { offset: 0x1111E3, size: 0x8, addend: 0x0, symName: '_$sSo27NSBitmapImageRepPropertyKeyaSYSCSY8rawValue03RawG0QzvgTW', symObjAddr: 0x1358, symBinAddr: 0x343C, symSize: 0x28 }
  - { offset: 0x111213, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0aB7ManagerC29requestPermissionForFirstTime33_3AE31ED88418F257D5A36F2DDFC489ADLLSbyFTf4d_n', symObjAddr: 0x1D94, symBinAddr: 0x3E78, symSize: 0x240 }
  - { offset: 0x11125D, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0aB7ManagerC17requestPermissionSbyFTf4d_n', symObjAddr: 0x1FD4, symBinAddr: 0x40B8, symSize: 0x134 }
  - { offset: 0x1113F9, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0aB7ManagerC05fetchA6AssetsSaySo7PHAssetCGyFTf4d_n', symObjAddr: 0x214C, symBinAddr: 0x4230, symSize: 0x2D8 }
  - { offset: 0x111599, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0aB7ManagerC03getA4Info3forAA0aF4DataVSgSo7PHAssetC_tFTf4nd_n', symObjAddr: 0x24DC, symBinAddr: 0x45C0, symSize: 0x6C8 }
  - { offset: 0x1118F2, size: 0x8, addend: 0x0, symName: '_$s18PhotoGalleryBridge0aB7ManagerC17generateThumbnail3for10targetSize10Foundation4DataVSgSo7PHAssetC_So6CGSizeVtFTf4nnd_n', symObjAddr: 0x2BA4, symBinAddr: 0x4C88, symSize: 0x280 }
  - { offset: 0x111C26, size: 0x8, addend: 0x0, symName: __swift_stdlib_malloc_size, symObjAddr: 0x36F0, symBinAddr: 0x57D4, symSize: 0x4 }
  - { offset: 0x111C34, size: 0x8, addend: 0x0, symName: __swift_stdlib_malloc_size, symObjAddr: 0x36F0, symBinAddr: 0x57D4, symSize: 0x4 }
...
