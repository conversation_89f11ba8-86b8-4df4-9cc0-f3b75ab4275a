{"builtTestProducts": [], "copyCommands": {}, "explicitTargetDependencyImportCheckingMode": {"none": {}}, "generatedSourceTargetSet": [], "pluginDescriptions": [], "swiftCommands": {"C.PhotoGalleryBridge-arm64-apple-macosx15.0-release.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/PhotoGalleryBridge.build/sources", "importPath": "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/Sources/PhotoGalleryBridge.swift"}, {"kind": "file", "name": "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/PhotoGalleryBridge.build/sources"}], "isLibrary": true, "moduleName": "PhotoGalleryBridge", "moduleOutputPath": "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/Modules/PhotoGalleryBridge.swiftmodule", "objects": ["/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/PhotoGalleryBridge.build/PhotoGalleryBridge.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx10.15", "-O", "-j8", "-DSWIFT_PACKAGE", "-module-cache-path", "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/PhotoGalleryBridge.build/PhotoGalleryBridge-Swift.h", "-swift-version", "5", "-enable-experimental-feature", "SymbolLinkageMarkers", "-Xcc", "-fvisibility=default", "-Xcc", "-I/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/include", "-Xcc", "-O0", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "swift"], "outputFileMapPath": "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/PhotoGalleryBridge.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/PhotoGalleryBridge.build/PhotoGalleryBridge.swift.o"}, {"kind": "file", "name": "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/Modules/PhotoGalleryBridge.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/Sources/PhotoGalleryBridge.swift"], "tempsPath": "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/PhotoGalleryBridge.build", "wholeModuleOptimization": true}}, "swiftFrontendCommands": {}, "swiftTargetScanArgs": {"PhotoGalleryBridge": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "PhotoGalleryBridge", "-package-name", "swift", "-whole-module-optimization", "-num-threads", "8", "-c", "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/Sources/PhotoGalleryBridge.swift", "-I", "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/Modules", "-target", "arm64-apple-macosx10.15", "-O", "-j8", "-DSWIFT_PACKAGE", "-module-cache-path", "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/PhotoGalleryBridge.build/PhotoGalleryBridge-Swift.h", "-swift-version", "5", "-enable-experimental-feature", "SymbolLinkageMarkers", "-Xcc", "-fvisibility=default", "-Xcc", "-I/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/include", "-Xcc", "-O0", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "swift", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"]}, "targetDependencyMap": {"PhotoGalleryBridge": []}, "testDiscoveryCommands": {}, "testEntryPointCommands": {}, "traitConfiguration": {"enableAllTraits": false}, "writeCommands": {"/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/PhotoGalleryBridge.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/Sources/PhotoGalleryBridge.swift"}], "outputFilePath": "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/PhotoGalleryBridge.build/sources"}, "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/PhotoGalleryBridge.product/Objects.LinkFileList": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<link-file-list>"}, {"kind": "file", "name": "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/PhotoGalleryBridge.build/PhotoGalleryBridge.swift.o"}], "outputFilePath": "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/PhotoGalleryBridge.product/Objects.LinkFileList"}, "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/swift-version--58304C5D6DBC2206.txt": {"alwaysOutOfDate": true, "inputs": [{"kind": "virtual", "name": "<swift-get-version>"}, {"kind": "file", "name": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"}], "outputFilePath": "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/swift-version--58304C5D6DBC2206.txt"}}}