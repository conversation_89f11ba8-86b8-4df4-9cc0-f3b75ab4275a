{"builtTestProducts": [], "copyCommands": {}, "explicitTargetDependencyImportCheckingMode": {"none": {}}, "generatedSourceTargetSet": [], "pluginDescriptions": [], "swiftCommands": {"C.PhotoGalleryBridge-arm64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/index-build/arm64-apple-macosx/debug/PhotoGalleryBridge.build/sources", "importPath": "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/index-build/arm64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/Sources/PhotoGalleryBridge.swift"}, {"kind": "file", "name": "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/index-build/arm64-apple-macosx/debug/PhotoGalleryBridge.build/sources"}], "isLibrary": true, "moduleName": "PhotoGalleryBridge", "moduleOutputPath": "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/index-build/arm64-apple-macosx/debug/Modules/PhotoGalleryBridge.swiftmodule", "objects": ["/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/index-build/arm64-apple-macosx/debug/PhotoGalleryBridge.build/PhotoGalleryBridge.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx10.15", "-enable-batch-mode", "-<PERSON><PERSON>", "-enable-testing", "-j8", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/index-build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/index-build/arm64-apple-macosx/debug/PhotoGalleryBridge.build/PhotoGalleryBridge-Swift.h", "-swift-version", "5", "-enable-experimental-feature", "SymbolLinkageMarkers", "-Xcc", "-fvisibility=default", "-Xcc", "-I/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/include", "-Xcc", "-O0", "-Xfrontend", "-experimental-lazy-typecheck", "-Xfrontend", "-experimental-skip-all-function-bodies", "-Xfrontend", "-experimental-allow-module-with-compiler-errors", "-Xfrontend", "-empty-abi-descriptor", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "swift"], "outputFileMapPath": "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/index-build/arm64-apple-macosx/debug/PhotoGalleryBridge.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/index-build/arm64-apple-macosx/debug/Modules/PhotoGalleryBridge.swiftmodule"}], "prepareForIndexing": true, "sources": ["/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/Sources/PhotoGalleryBridge.swift"], "tempsPath": "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/index-build/arm64-apple-macosx/debug/PhotoGalleryBridge.build", "wholeModuleOptimization": false}}, "swiftFrontendCommands": {}, "swiftTargetScanArgs": {"PhotoGalleryBridge": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "PhotoGalleryBridge", "-package-name", "swift", "-incremental", "-c", "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/Sources/PhotoGalleryBridge.swift", "-I", "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/index-build/arm64-apple-macosx/debug/Modules", "-target", "arm64-apple-macosx10.15", "-enable-batch-mode", "-<PERSON><PERSON>", "-enable-testing", "-j8", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/index-build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/index-build/arm64-apple-macosx/debug/PhotoGalleryBridge.build/PhotoGalleryBridge-Swift.h", "-swift-version", "5", "-enable-experimental-feature", "SymbolLinkageMarkers", "-Xcc", "-fvisibility=default", "-Xcc", "-I/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/include", "-Xcc", "-O0", "-Xfrontend", "-experimental-lazy-typecheck", "-Xfrontend", "-experimental-skip-all-function-bodies", "-Xfrontend", "-experimental-allow-module-with-compiler-errors", "-Xfrontend", "-empty-abi-descriptor", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "swift", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"]}, "targetDependencyMap": {"PhotoGalleryBridge": []}, "testDiscoveryCommands": {}, "testEntryPointCommands": {}, "traitConfiguration": {"enableAllTraits": false}, "writeCommands": {"/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/index-build/arm64-apple-macosx/debug/PhotoGalleryBridge.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/Sources/PhotoGalleryBridge.swift"}], "outputFilePath": "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/index-build/arm64-apple-macosx/debug/PhotoGalleryBridge.build/sources"}, "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt": {"alwaysOutOfDate": true, "inputs": [{"kind": "virtual", "name": "<swift-get-version>"}, {"kind": "file", "name": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"}], "outputFilePath": "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}}}