client:
  name: basic
  file-system: device-agnostic
tools: {}
targets:
  "PackageStructure": ["<PackageStructure>"]
  "PhotoGalleryBridge-arm64-apple-macosx15.0-release.dylib": ["<PhotoGalleryBridge-arm64-apple-macosx15.0-release.dylib>"]
  "PhotoGalleryBridge-arm64-apple-macosx15.0-release.module": ["<PhotoGalleryBridge-arm64-apple-macosx15.0-release.module>"]
  "main": ["<PhotoGalleryBridge-arm64-apple-macosx15.0-release.dylib>","<PhotoGalleryBridge-arm64-apple-macosx15.0-release.module>"]
  "test": ["<PhotoGalleryBridge-arm64-apple-macosx15.0-release.dylib>","<PhotoGalleryBridge-arm64-apple-macosx15.0-release.module>"]
default: "main"
nodes:
  "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/":
    is-directory-structure: true
    content-exclusion-patterns: [".git",".build"]
commands:
  "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/PhotoGalleryBridge.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/Sources/PhotoGalleryBridge.swift"]
    outputs: ["/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/PhotoGalleryBridge.build/sources"]
    description: "Write auxiliary file /Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/PhotoGalleryBridge.build/sources"

  "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/PhotoGalleryBridge.product/Objects.LinkFileList":
    tool: write-auxiliary-file
    inputs: ["<link-file-list>","/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/PhotoGalleryBridge.build/PhotoGalleryBridge.swift.o"]
    outputs: ["/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/PhotoGalleryBridge.product/Objects.LinkFileList"]
    description: "Write auxiliary file /Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/PhotoGalleryBridge.product/Objects.LinkFileList"

  "/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/swift-version--58304C5D6DBC2206.txt":
    tool: write-auxiliary-file
    inputs: ["<swift-get-version>","/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"]
    outputs: ["/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/swift-version--58304C5D6DBC2206.txt"]
    always-out-of-date: "true"
    description: "Write auxiliary file /Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/swift-version--58304C5D6DBC2206.txt"

  "<PhotoGalleryBridge-arm64-apple-macosx15.0-release.dylib>":
    tool: phony
    inputs: ["/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/libPhotoGalleryBridge.dylib"]
    outputs: ["<PhotoGalleryBridge-arm64-apple-macosx15.0-release.dylib>"]

  "<PhotoGalleryBridge-arm64-apple-macosx15.0-release.module>":
    tool: phony
    inputs: ["/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/PhotoGalleryBridge.build/PhotoGalleryBridge.swift.o","/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/Modules/PhotoGalleryBridge.swiftmodule"]
    outputs: ["<PhotoGalleryBridge-arm64-apple-macosx15.0-release.module>"]

  "C.PhotoGalleryBridge-arm64-apple-macosx15.0-release.dylib":
    tool: shell
    inputs: ["/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/PhotoGalleryBridge.build/PhotoGalleryBridge.swift.o","/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/PhotoGalleryBridge.product/Objects.LinkFileList"]
    outputs: ["/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/libPhotoGalleryBridge.dylib"]
    description: "Linking ./.build/arm64-apple-macosx/release/libPhotoGalleryBridge.dylib"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-L","/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release","-o","/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/libPhotoGalleryBridge.dylib","-module-name","PhotoGalleryBridge","-Xlinker","-no_warn_duplicate_libraries","-emit-library","-Xlinker","-install_name","-Xlinker","@rpath/libPhotoGalleryBridge.dylib","-Xlinker","-dead_strip","-Xlinker","-rpath","-Xlinker","@loader_path","@/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/PhotoGalleryBridge.product/Objects.LinkFileList","-enable-experimental-feature","SymbolLinkageMarkers","-Xlinker","-rpath","-Xlinker","/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift-5.5/macosx","-target","arm64-apple-macosx10.15","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g"]

  "C.PhotoGalleryBridge-arm64-apple-macosx15.0-release.module":
    tool: shell
    inputs: ["/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/Sources/PhotoGalleryBridge.swift","/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/swift-version--58304C5D6DBC2206.txt","/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/PhotoGalleryBridge.build/sources"]
    outputs: ["/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/PhotoGalleryBridge.build/PhotoGalleryBridge.swift.o","/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/Modules/PhotoGalleryBridge.swiftmodule"]
    description: "Compiling Swift Module 'PhotoGalleryBridge' (1 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","PhotoGalleryBridge","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/Modules/PhotoGalleryBridge.swiftmodule","-output-file-map","/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/PhotoGalleryBridge.build/output-file-map.json","-parse-as-library","-whole-module-optimization","-num-threads","8","-c","@/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/PhotoGalleryBridge.build/sources","-I","/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/Modules","-target","arm64-apple-macosx10.15","-O","-j8","-DSWIFT_PACKAGE","-module-cache-path","/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/ModuleCache","-parseable-output","-parse-as-library","-emit-objc-header","-emit-objc-header-path","/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/.build/arm64-apple-macosx/release/PhotoGalleryBridge.build/PhotoGalleryBridge-Swift.h","-swift-version","5","-enable-experimental-feature","SymbolLinkageMarkers","-Xcc","-fvisibility=default","-Xcc","-I/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/include","-Xcc","-O0","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-package-name","swift"]

  "PackageStructure":
    tool: package-structure-tool
    inputs: ["/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/","/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/Package.swift","/Users/<USER>/local_doc/l_dev/my/rust/photo-dup/photo-dup/src-tauri/swift/Package.resolved"]
    outputs: ["<PackageStructure>"]
    description: "Planning build"
    allow-missing-inputs: true

