// swift-tools-version: 5.9
import PackageDescription

let package = Package(
    name: "PhotoGalleryBridge",
    platforms: [
        .macOS(.v10_15)
    ],
    products: [
        .library(
            name: "PhotoGalleryBridge",
            type: .dynamic,
            targets: ["PhotoGalleryBridge"]
        )
    ],
    dependencies: [],
    targets: [
        .target(
            name: "PhotoGalleryBridge",
            dependencies: [],
            path: ".",
            sources: ["Sources"],
            publicHeadersPath: "include",
            cSettings: [
                .headerSearchPath("include"),
                .unsafeFlags(["-O0"], .when(platforms: [.macOS]))
            ],
            swiftSettings: [
                .enableExperimentalFeature("SymbolLinkageMarkers"),
                .unsafeFlags(["-Xcc", "-fvisibility=default"], .when(platforms: [.macOS]))
            ]
        )
    ]
)