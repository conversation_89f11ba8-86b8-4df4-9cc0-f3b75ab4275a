import AppKit  // 添加 AppKit 导入以使用 NSImage 和 NSBitmapImageRep
import Foundation
import Photos

// C 结构体定义（需要与 Rust 端匹配）
public struct PhotoAssetStruct {
    var identifier: UnsafePointer<CChar>?
    var creation_date: Int64
    var modification_date: Int64
    var media_type: CInt
    var has_location: Bool
    var latitude: Double
    var longitude: Double

    init(
        identifier: UnsafePointer<CChar>?, creation_date: Int64, modification_date: Int64,
        media_type: CInt, has_location: Bool, latitude: Double, longitude: Double
    ) {
        self.identifier = identifier
        self.creation_date = creation_date
        self.modification_date = modification_date
        self.media_type = media_type
        self.has_location = has_location
        self.latitude = latitude
        self.longitude = longitude
    }
}

public struct PhotoInfoStruct {
    var file_name: UnsafePointer<CChar>?
    var file_size: Int64
    var width: UInt32
    var height: UInt32
    var format: CInt
    var creation_date: Int64

    init(
        file_name: UnsafePointer<CChar>?, file_size: Int64, width: UInt32, height: UInt32,
        format: CInt, creation_date: Int64
    ) {
        self.file_name = file_name
        self.file_size = file_size
        self.width = width
        self.height = height
        self.format = format
        self.creation_date = creation_date
    }
}

public struct ThumbnailDataStruct {
    var data: UnsafePointer<UInt8>?
    var length: Int
    var width: UInt32
    var height: UInt32

    init(data: UnsafePointer<UInt8>?, length: Int, width: UInt32, height: UInt32) {
        self.data = data
        self.length = length
        self.width = width
        self.height = height
    }
}

public struct PhotoAssetArray {
    var assets: UnsafeMutablePointer<PhotoAssetStruct>?
    var count: Int
}

public typealias PhotoAsset = PhotoAssetStruct
public typealias PhotoInfo = PhotoInfoStruct
public typealias ThumbnailData = ThumbnailDataStruct
public typealias MediaType = CInt
public typealias ImageFormat = CInt

// 枚举值（需要与 Rust 端保持一致）
let MediaTypeUnknown: MediaType = 0
let MediaTypeImage: MediaType = 1
let MediaTypeVideo: MediaType = 2
let MediaTypeAudio: MediaType = 3

let ImageFormatUnknown: ImageFormat = 0
let ImageFormatJPEG: ImageFormat = 1
let ImageFormatPNG: ImageFormat = 2
let ImageFormatHEIC: ImageFormat = 3
let ImageFormatRAW: ImageFormat = 4

class PhotoGalleryManager {
    static let shared = PhotoGalleryManager()
    private init() {}

    // 请求相册访问权限
    func requestPermission() -> Bool {
        let status: PHAuthorizationStatus

        if #available(macOS 11.0, *) {
            status = PHPhotoLibrary.authorizationStatus(for: .readWrite)
        } else {
            status = PHPhotoLibrary.authorizationStatus()
        }

        switch status {
        case .authorized:
            return true
        case .notDetermined:
            return requestPermissionForFirstTime()
        case .limited:
            // 有限权限也认为是有效的
            return true
        case .restricted, .denied:
            setErrorInfo(
                .permissionDenied, "Permission denied", "用户拒绝了相册访问权限，请在系统设置 > 隐私与安全性 > 照片中允许此应用访问")
            return false
        @unknown default:
            setErrorInfo(.unknown, "Unknown permission status", "未知的权限状态")
            return false
        }
    }

    // 首次请求权限
    private func requestPermissionForFirstTime() -> Bool {
        let semaphore = DispatchSemaphore(value: 0)
        var granted = false

        if #available(macOS 11.0, *) {
            PHPhotoLibrary.requestAuthorization(for: .readWrite) { status in
                granted = (status == .authorized || status == .limited)
                if !granted {
                    setErrorInfo(
                        .permissionDenied, "Permission denied",
                        "用户拒绝了相册访问权限，请在系统设置 > 隐私与安全性 > 照片中允许此应用访问")
                }
                semaphore.signal()
            }
        } else {
            PHPhotoLibrary.requestAuthorization { status in
                granted = (status == .authorized || status == .limited)
                if !granted {
                    setErrorInfo(
                        .permissionDenied, "Permission denied",
                        "用户拒绝了相册访问权限，请在系统设置 > 隐私与安全性 > 照片中允许此应用访问")
                }
                semaphore.signal()
            }
        }

        _ = semaphore.wait(timeout: .distantFuture)
        return granted
    }

    // 获取相片资源
    func fetchPhotoAssets() -> [PHAsset] {
        let status: PHAuthorizationStatus

        if #available(macOS 11.0, *) {
            status = PHPhotoLibrary.authorizationStatus(for: .readWrite)
        } else {
            status = PHPhotoLibrary.authorizationStatus()
        }

        guard status == .authorized || status == .limited else {
            setErrorInfo(
                .permissionDenied, "Permission denied", "需要访问相册权限，请在系统设置 > 隐私与安全性 > 照片中允许应用访问相册")
            return []
        }

        let fetchOptions = PHFetchOptions()
        fetchOptions.sortDescriptors = [
            NSSortDescriptor(key: "creationDate", ascending: false)
        ]

        // 只获取图片资源
        let fetchResult = PHAsset.fetchAssets(with: .image, options: fetchOptions)

        var assets: [PHAsset] = []
        fetchResult.enumerateObjects { asset, _, _ in
            assets.append(asset)
        }

        return assets
    }

    // 获取相片详细信息
    func getPhotoInfo(for asset: PHAsset) -> PhotoInfoData? {
        let resources = PHAssetResource.assetResources(for: asset)
        guard let resource = resources.first else {
            setErrorInfo(.assetNotFound, "No resource found for asset", "相片资源未找到")
            return nil
        }

        // 获取文件名
        let fileName = resource.originalFilename

        // 获取文件大小
        let fileSize = resource.value(forKey: "fileSize") as? Int64 ?? 0

        // 获取图像尺寸
        let width = UInt32(asset.pixelWidth)
        let height = UInt32(asset.pixelHeight)

        // 确定图像格式
        let format = self.getImageFormat(from: resource.uniformTypeIdentifier)

        // 获取创建日期
        let creationDate = asset.creationDate?.timeIntervalSince1970 ?? 0

        // 获取位置信息
        var location: (Double, Double)? = nil
        if let assetLocation = asset.location {
            location = (assetLocation.coordinate.latitude, assetLocation.coordinate.longitude)
        }

        return PhotoInfoData(
            fileName: fileName,
            fileSize: fileSize,
            width: width,
            height: height,
            format: format,
            creationDate: Int64(creationDate),
            location: location
        )
    }

    // 生成缩略图
    func generateThumbnail(for asset: PHAsset, targetSize: CGSize) -> Data? {
        let options = PHImageRequestOptions()
        options.isSynchronous = true
        options.resizeMode = .exact
        options.deliveryMode = .highQualityFormat
        options.isNetworkAccessAllowed = true

        var thumbnailData: Data?
        let semaphore = DispatchSemaphore(value: 0)

        // 使用正确的 macOS Photos API
        PHImageManager.default().requestImage(
            for: asset,
            targetSize: targetSize,
            contentMode: PHImageContentMode.aspectFit,
            options: options
        ) { image, info in
            if let image = image {
                // 转换为 JPEG 数据以获得更好的兼容性
                #if os(iOS)
                    thumbnailData = image.jpegData(compressionQuality: 0.8)
                #else
                    // macOS 处理
                    if let tiffData = image.tiffRepresentation,
                        let bitmapRep = NSBitmapImageRep(data: tiffData)
                    {
                        let jpegData = bitmapRep.representation(
                            using: .jpeg, properties: [.compressionFactor: 0.8])
                        thumbnailData = jpegData
                    }
                #endif
            }
            semaphore.signal()
        }

        _ = semaphore.wait(timeout: .distantFuture)
        return thumbnailData
    }

    // 获取图像格式
    private func getImageFormat(from uti: String) -> ImageFormat {
        if uti.contains("jpeg") || uti.contains("jpg") {
            return ImageFormatJPEG
        } else if uti.contains("png") {
            return ImageFormatPNG
        } else if uti.contains("heic") || uti.contains("heif") {
            return ImageFormatHEIC
        } else if uti.contains("raw") || uti.contains("dng") {
            return ImageFormatRAW
        } else {
            return ImageFormatUnknown
        }
    }

    // 获取错误信息
    func getErrorMessage() -> String? {
        return lastErrorMessage
    }

    // 清理错误信息
    func clearErrorMessage() {
        lastErrorInfo = nil
    }
}

// 内部数据结构
struct PhotoInfoData {
    let fileName: String
    let fileSize: Int64
    let width: UInt32
    let height: UInt32
    let format: ImageFormat
    let creationDate: Int64
    let location: (Double, Double)?
}

// 错误类型枚举
enum PhotoGalleryError: Int32 {
    case none = 0
    case permissionDenied = 1
    case assetNotFound = 2
    case invalidFormat = 3
    case memoryError = 4
    case unknown = 5
}

// 错误信息结构
struct ErrorInfo {
    let errorCode: PhotoGalleryError
    let errorMessage: String
    let userMessage: String
}

// 全局错误信息
private var lastErrorInfo: ErrorInfo? = nil

// 便捷的错误消息获取器
private var lastErrorMessage: String? {
    return lastErrorInfo?.errorMessage
}

// 使用 @_silgen_name 强制导出 C 符号
@_used @_silgen_name("request_photo_gallery_permission")
public func request_photo_gallery_permission() -> Bool {
    return PhotoGalleryManager.shared.requestPermission()
}

@_used @_silgen_name("fetch_photo_assets")
public func fetch_photo_assets() -> UnsafeMutablePointer<PhotoAssetArray>? {
    let assets = PhotoGalleryManager.shared.fetchPhotoAssets()

    guard !assets.isEmpty else {
        if let errorMsg = PhotoGalleryManager.shared.getErrorMessage() {
            lastErrorInfo = ErrorInfo(
                errorCode: .unknown, errorMessage: errorMsg, userMessage: errorMsg)
        } else {
            setErrorInfo(.assetNotFound, "No photos found", "没有找到相片")
        }
        return nil
    }

    let count = assets.count
    let assetsArray = UnsafeMutablePointer<PhotoAssetArray>.allocate(capacity: 1)
    let photoAssets = UnsafeMutablePointer<PhotoAssetStruct>.allocate(capacity: count)

    assetsArray.pointee.assets = photoAssets
    assetsArray.pointee.count = count

    for (index, asset) in assets.enumerated() {
        let creationDate = Int64(asset.creationDate?.timeIntervalSince1970 ?? 0)
        let modificationDate = Int64(asset.modificationDate?.timeIntervalSince1970 ?? 0)
        let mediaType: CInt

        switch asset.mediaType {
        case .image:
            mediaType = MediaTypeImage
        case .video:
            mediaType = MediaTypeVideo
        case .audio:
            mediaType = MediaTypeAudio
        case .unknown:
            mediaType = MediaTypeUnknown
        @unknown default:
            mediaType = MediaTypeUnknown
        }

        let (hasLocation, latitude, longitude) = {
            if let location = asset.location {
                (true, location.coordinate.latitude, location.coordinate.longitude)
            } else {
                (false, 0.0, 0.0)
            }
        }()

        let identifier = asset.localIdentifier.cString(using: String.Encoding.utf8).map {
            UnsafePointer(strdup($0))
        }

        let photoAsset = PhotoAssetStruct(
            identifier: identifier,
            creation_date: creationDate,
            modification_date: modificationDate,
            media_type: mediaType,
            has_location: hasLocation,
            latitude: latitude,
            longitude: longitude
        )

        photoAssets[index] = photoAsset
    }

    return assetsArray
}

@_used @_silgen_name("generate_thumbnail")
public func generate_thumbnail(asset_id: UnsafePointer<CChar>, width: UInt32, height: UInt32)
    -> UnsafeMutablePointer<ThumbnailData>?
{
    let identifier = String(cString: asset_id)

    let fetchResult = PHAsset.fetchAssets(withLocalIdentifiers: [identifier], options: nil)
    guard let asset = fetchResult.firstObject else {
        setErrorInfo(.assetNotFound, "Asset not found", "相片资源未找到")
        return nil
    }

    let targetSize = CGSize(width: CGFloat(width), height: CGFloat(height))

    guard
        let imageData = PhotoGalleryManager.shared.generateThumbnail(
            for: asset, targetSize: targetSize)
    else {
        if let errorMsg = PhotoGalleryManager.shared.getErrorMessage() {
            lastErrorInfo = ErrorInfo(
                errorCode: .unknown, errorMessage: errorMsg, userMessage: errorMsg)
        } else {
            setErrorInfo(.invalidFormat, "Failed to generate thumbnail", "生成缩略图失败")
        }
        return nil
    }

    let dataPointer = UnsafeMutablePointer<UInt8>.allocate(capacity: imageData.count)
    imageData.copyBytes(to: dataPointer, count: imageData.count)

    let thumbnailData = ThumbnailDataStruct(
        data: dataPointer,
        length: imageData.count,
        width: width,
        height: height
    )

    let thumbnailPointer = UnsafeMutablePointer<ThumbnailData>.allocate(capacity: 1)
    thumbnailPointer.pointee = thumbnailData

    return thumbnailPointer
}

@_used @_silgen_name("get_photo_info")
public func get_photo_info(asset_id: UnsafePointer<CChar>) -> UnsafeMutablePointer<PhotoInfo>? {
    let identifier = String(cString: asset_id)

    let fetchResult = PHAsset.fetchAssets(withLocalIdentifiers: [identifier], options: nil)
    guard let asset = fetchResult.firstObject else {
        setErrorInfo(.assetNotFound, "Asset not found", "相片资源未找到")
        return nil
    }

    guard let photoInfoData = PhotoGalleryManager.shared.getPhotoInfo(for: asset) else {
        if let errorMsg = PhotoGalleryManager.shared.getErrorMessage() {
            lastErrorInfo = ErrorInfo(
                errorCode: .unknown, errorMessage: errorMsg, userMessage: errorMsg)
        } else {
            setErrorInfo(.invalidFormat, "Failed to get photo info", "获取相片信息失败")
        }
        return nil
    }

    let fileName = photoInfoData.fileName.cString(using: String.Encoding.utf8).map {
        UnsafePointer(strdup($0))
    }

    let photoInfo = PhotoInfoStruct(
        file_name: fileName,
        file_size: photoInfoData.fileSize,
        width: photoInfoData.width,
        height: photoInfoData.height,
        format: photoInfoData.format,
        creation_date: photoInfoData.creationDate
    )

    let photoInfoPointer = UnsafeMutablePointer<PhotoInfo>.allocate(capacity: 1)
    photoInfoPointer.pointee = photoInfo

    return photoInfoPointer
}

@_used @_silgen_name("free_photo_asset_array")
public func free_photo_asset_array(array: UnsafeMutablePointer<PhotoAssetArray>?) {
    guard let array = array else { return }

    let arrayRef = array.pointee
    if let assets = arrayRef.assets {
        for i in 0..<arrayRef.count {
            let asset = assets.advanced(by: i)
            if asset.pointee.identifier != nil {
                free(UnsafeMutablePointer(mutating: asset.pointee.identifier!))
            }
        }
        assets.deallocate()
    }
    array.deallocate()
}

@_used @_silgen_name("free_thumbnail_data")
public func free_thumbnail_data(data: UnsafeMutablePointer<ThumbnailData>?) {
    guard let data = data else { return }

    let dataRef = data.pointee
    if dataRef.data != nil {
        dataRef.data?.deallocate()
    }
    data.deallocate()
}

@_used @_silgen_name("free_photo_info")
public func free_photo_info(info: UnsafeMutablePointer<PhotoInfo>?) {
    guard let info = info else { return }

    let infoRef = info.pointee
    if infoRef.file_name != nil {
        infoRef.file_name?.deallocate()
    }
    info.deallocate()
}

@_used @_silgen_name("get_last_error_message")
public func getLastErrorMessage() -> UnsafePointer<CChar>? {
    if let errorMessage = lastErrorMessage {
        return errorMessage.withCString { ptr in
            return UnsafePointer(strdup(ptr))
        }
    }
    return nil
}

// 添加获取错误代码的函数
@_used @_silgen_name("get_last_error_code")
public func getLastErrorCode() -> Int32 {
    return lastErrorInfo?.errorCode.rawValue ?? PhotoGalleryError.none.rawValue
}

// 辅助函数：设置错误信息
private func setErrorInfo(
    _ errorCode: PhotoGalleryError, _ errorMessage: String, _ userMessage: String
) {
    lastErrorInfo = ErrorInfo(
        errorCode: errorCode,
        errorMessage: errorMessage,
        userMessage: userMessage
    )
}

// 辅助函数：字符串复制
private func strdup(_ string: UnsafePointer<CChar>) -> UnsafeMutablePointer<CChar> {
    let length = strlen(string)
    let buffer = UnsafeMutablePointer<CChar>.allocate(capacity: length + 1)
    memcpy(buffer, string, length)
    buffer[length] = 0
    return buffer
}
