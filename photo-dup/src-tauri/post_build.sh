#!/bin/bash

# Tauri 构建后钩子脚本
# 自动复制 Swift 动态库到应用包

set -e

echo "运行构建后钩子..."

# 获取应用包路径
APP_BUNDLE_PATH="$1"
if [ -z "$APP_BUNDLE_PATH" ]; then
    echo "错误: 未提供应用包路径"
    exit 1
fi

# 检查应用包是否存在
if [ ! -d "$APP_BUNDLE_PATH" ]; then
    echo "错误: 应用包不存在: $APP_BUNDLE_PATH"
    exit 1
fi

# 查找动态库
DYLIB_PATH="target/release/libPhotoGalleryBridge.dylib"
if [ ! -f "$DYLIB_PATH" ]; then
    echo "错误: 动态库不存在: $DYLIB_PATH"
    exit 1
fi

# 复制动态库到应用包
MACOS_DIR="$APP_BUNDLE_PATH/Contents/MacOS"
if [ ! -d "$MACOS_DIR" ]; then
    echo "错误: MacOS 目录不存在: $MACOS_DIR"
    exit 1
fi

echo "复制动态库: $DYLIB_PATH -> $MACOS_DIR/"
cp "$DYLIB_PATH" "$MACOS_DIR/"

# 验证复制是否成功
DYLIB_NAME="libPhotoGalleryBridge.dylib"
if [ -f "$MACOS_DIR/$DYLIB_NAME" ]; then
    echo "✅ 动态库复制成功"
else
    echo "❌ 动态库复制失败"
    exit 1
fi

echo "构建后钩子完成"
