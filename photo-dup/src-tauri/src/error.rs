use thiserror::Error;
use std::fmt;

/// 应用程序错误类型
#[derive(Debug, Error)]
pub enum AppError {
    /// 权限相关错误
    #[error("权限错误: {0}")]
    PermissionDenied(String),
    
    /// 系统错误
    #[error("系统错误: {0}")]
    SystemError(String),
    
    /// 网络错误
    #[error("网络错误: {0}")]
    NetworkError(String),
    
    /// 数据错误
    #[error("数据错误: {0}")]
    DataError(String),
    
    /// 缓存错误
    #[error("缓存错误: {0}")]
    CacheError(String),
    
    /// 用户错误
    #[error("用户错误: {0}")]
    UserError(String),
    
    /// FFI 错误
    #[error("FFI 错误: {0}")]
    FfiError(String),
    
    /// 序列化错误
    #[error("序列化错误: {0}")]
    SerializationError(String),
    
    /// IO 错误
    #[error("IO 错误: {0}")]
    IoError(String),
}

impl AppError {
    /// 创建权限拒绝错误
    pub fn permission_denied(msg: impl Into<String>) -> Self {
        AppError::PermissionDenied(msg.into())
    }

    /// 创建系统错误
    pub fn system_error(msg: impl Into<String>) -> Self {
        AppError::SystemError(msg.into())
    }

    /// 创建数据错误
    pub fn data_error(msg: impl Into<String>) -> Self {
        AppError::DataError(msg.into())
    }

    /// 创建缓存错误
    pub fn cache_error(msg: impl Into<String>) -> Self {
        AppError::CacheError(msg.into())
    }

    /// 创建用户错误
    pub fn user_error(msg: impl Into<String>) -> Self {
        AppError::UserError(msg.into())
    }

    /// 创建 FFI 错误
    pub fn ffi_error(msg: impl Into<String>) -> Self {
        AppError::FfiError(msg.into())
    }

    /// 获取错误代码
    pub fn error_code(&self) -> u32 {
        match self {
            AppError::PermissionDenied(_) => 1001,
            AppError::SystemError(_) => 2001,
            AppError::NetworkError(_) => 3001,
            AppError::DataError(_) => 4001,
            AppError::CacheError(_) => 5001,
            AppError::UserError(_) => 6001,
            AppError::FfiError(_) => 7001,
            AppError::SerializationError(_) => 8001,
            AppError::IoError(_) => 9001,
        }
    }

    /// 获取错误类型
    pub fn error_type(&self) -> &'static str {
        match self {
            AppError::PermissionDenied(_) => "PermissionDenied",
            AppError::SystemError(_) => "SystemError",
            AppError::NetworkError(_) => "NetworkError",
            AppError::DataError(_) => "DataError",
            AppError::CacheError(_) => "CacheError",
            AppError::UserError(_) => "UserError",
            AppError::FfiError(_) => "FfiError",
            AppError::SerializationError(_) => "SerializationError",
            AppError::IoError(_) => "IoError",
        }
    }

    /// 判断是否为用户可恢复的错误
    pub fn is_recoverable(&self) -> bool {
        matches!(self, 
            AppError::PermissionDenied(_) | 
            AppError::UserError(_) |
            AppError::CacheError(_) |
            AppError::NetworkError(_)
        )
    }

    /// 判断是否为严重错误
    pub fn is_critical(&self) -> bool {
        matches!(self, 
            AppError::SystemError(_) | 
            AppError::FfiError(_) |
            AppError::DataError(_)
        )
    }

    /// 获取用户友好的错误消息
    pub fn user_message(&self) -> String {
        match self {
            AppError::PermissionDenied(msg) => {
                if msg.contains("权限拒绝") || msg.contains("Permission denied") {
                    "需要访问相册权限，请在系统设置中允许应用访问相册".to_string()
                } else {
                    format!("权限问题: {}", msg)
                }
            },
            AppError::UserError(msg) => format!("操作失败: {}", msg),
            AppError::CacheError(msg) => format!("缓存问题: {}", msg),
            AppError::NetworkError(msg) => format!("网络问题: {}", msg),
            AppError::SystemError(msg) => format!("系统错误，请稍后重试: {}", msg),
            AppError::DataError(msg) => format!("数据错误: {}", msg),
            AppError::FfiError(msg) => format!("接口错误: {}", msg),
            AppError::SerializationError(msg) => format!("数据格式错误: {}", msg),
            AppError::IoError(msg) => format!("文件操作失败: {}", msg),
        }
    }
}

/// 从其他错误类型转换
impl From<std::io::Error> for AppError {
    fn from(err: std::io::Error) -> Self {
        AppError::IoError(err.to_string())
    }
}

impl From<serde_json::Error> for AppError {
    fn from(err: serde_json::Error) -> Self {
        AppError::SerializationError(err.to_string())
    }
}

impl From<crate::cache::CacheError> for AppError {
    fn from(err: crate::cache::CacheError) -> Self {
        AppError::cache_error(err.to_string())
    }
}

impl From<crate::models::PhotoGalleryError> for AppError {
    fn from(err: crate::models::PhotoGalleryError) -> Self {
        match err {
            crate::models::PhotoGalleryError::PermissionDenied => {
                AppError::permission_denied("访问相册权限被拒绝")
            },
            crate::models::PhotoGalleryError::AssetNotFound => {
                AppError::data_error("相片资源未找到")
            },
            crate::models::PhotoGalleryError::InvalidFormat => {
                AppError::data_error("数据格式无效")
            },
            crate::models::PhotoGalleryError::MemoryError => {
                AppError::system_error("内存分配失败")
            },
            crate::models::PhotoGalleryError::Unknown => {
                AppError::system_error("未知错误")
            },
            crate::models::PhotoGalleryError::None => {
                AppError::system_error("未指定的错误")
            },
        }
    }
}

/// 错误处理结果类型
pub type AppResult<T> = Result<T, AppError>;

/// 错误日志记录器
pub struct ErrorLogger;

impl ErrorLogger {
    /// 记录错误
    pub fn log_error(error: &AppError) {
        let error_info = serde_json::json!({
            "timestamp": chrono::Utc::now().to_rfc3339(),
            "error_code": error.error_code(),
            "error_type": error.error_type(),
            "message": error.to_string(),
            "user_message": error.user_message(),
            "is_recoverable": error.is_recoverable(),
            "is_critical": error.is_critical()
        });

        log::error!("错误日志: {}", serde_json::to_string(&error_info).unwrap_or_default());
    }

    /// 记录带上下文的错误
    pub fn log_error_with_context(error: &AppError, context: &str) {
        let error_info = serde_json::json!({
            "timestamp": chrono::Utc::now().to_rfc3339(),
            "context": context,
            "error_code": error.error_code(),
            "error_type": error.error_type(),
            "message": error.to_string(),
            "user_message": error.user_message(),
            "is_recoverable": error.is_recoverable(),
            "is_critical": error.is_critical()
        });

        log::error!("错误日志 [{}]: {}", context, serde_json::to_string(&error_info).unwrap_or_default());
    }
}

/// 错误重试策略
pub trait RetryStrategy {
    fn should_retry(&self, error: &AppError, attempt: u32) -> bool;
    fn delay(&self, attempt: u32) -> std::time::Duration;
}

/// 指数退避重试策略
pub struct ExponentialBackoff {
    max_attempts: u32,
    base_delay: std::time::Duration,
    max_delay: std::time::Duration,
}

impl ExponentialBackoff {
    pub fn new(max_attempts: u32, base_delay: std::time::Duration, max_delay: std::time::Duration) -> Self {
        Self {
            max_attempts,
            base_delay,
            max_delay,
        }
    }
}

impl RetryStrategy for ExponentialBackoff {
    fn should_retry(&self, error: &AppError, attempt: u32) -> bool {
        attempt < self.max_attempts && error.is_recoverable()
    }

    fn delay(&self, attempt: u32) -> std::time::Duration {
        let delay = self.base_delay * 2u32.pow(attempt.saturating_sub(1));
        delay.min(self.max_delay)
    }
}

/// 重试执行函数
pub async fn retry_with_strategy<F, T>(
    strategy: &dyn RetryStrategy,
    operation: F,
) -> AppResult<T>
where
    F: Fn() -> futures::future::BoxFuture<'static, AppResult<T>>,
{
    let mut attempt = 1;
    
    loop {
        match operation().await {
            Ok(result) => return Ok(result),
            Err(error) => {
                if strategy.should_retry(&error, attempt) {
                    let delay = strategy.delay(attempt);
                    log::warn!("操作失败，第 {} 次重试，延迟 {:?}: {}", attempt, delay, error);
                    tokio::time::sleep(delay).await;
                    attempt += 1;
                } else {
                    ErrorLogger::log_error_with_context(&error, &format!("重试失败，共尝试 {} 次", attempt));
                    return Err(error);
                }
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_app_error_creation() {
        let error = AppError::permission_denied("测试权限错误");
        assert_eq!(error.error_code(), 1001);
        assert_eq!(error.error_type(), "PermissionDenied");
        assert!(error.is_recoverable());
        assert!(!error.is_critical());
    }

    #[test]
    fn test_user_message() {
        let error = AppError::permission_denied("Permission denied");
        let user_msg = error.user_message();
        assert!(user_msg.contains("需要访问相册权限"));
    }

    #[test]
    fn test_exponential_backoff() {
        let strategy = ExponentialBackoff::new(3, std::time::Duration::from_millis(100), std::time::Duration::from_secs(1));
        
        let error = AppError::network_error("连接超时");
        assert!(strategy.should_retry(&error, 1));
        assert!(strategy.should_retry(&error, 2));
        assert!(!strategy.should_retry(&error, 3));
        
        let critical_error = AppError::system_error("系统崩溃");
        assert!(!strategy.should_retry(&critical_error, 1));
    }

    #[tokio::test]
    async fn test_retry_mechanism() {
        let strategy = ExponentialBackoff::new(3, std::time::Duration::from_millis(10), std::time::Duration::from_millis(100));
        
        let mut attempt = 0;
        let result = retry_with_strategy(&strategy, || {
            attempt += 1;
            Box::pin(async move {
                if attempt < 3 {
                    Err(AppError::network_error("临时网络错误"))
                } else {
                    Ok("成功")
                }
            })
        }).await;
        
        assert_eq!(result.unwrap(), "成功");
        assert_eq!(attempt, 3);
    }
}