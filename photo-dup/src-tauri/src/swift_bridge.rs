use std::ffi::{CStr, CString};
use std::os::raw::{c_char, c_void};
use crate::models::*;

// 外部 Swift 函数声明
#[link(name = "PhotoGalleryBridge", kind = "static")]
extern "C" {
    fn request_photo_gallery_permission() -> bool;
    fn fetch_photo_assets() -> *mut PhotoAssetArray;
    fn generate_thumbnail(asset_id: *const c_char, width: u32, height: u32) -> *mut ThumbnailData;
    fn get_photo_info(asset_id: *const c_char) -> *mut PhotoInfo;
    fn free_photo_asset_array(array: *mut PhotoAssetArray);
    fn free_thumbnail_data(data: *mut ThumbnailData);
    fn free_photo_info(info: *mut PhotoInfo);
    fn get_last_error_message() -> *const c_char;
    fn get_last_error_code() -> i32;
}

pub struct SwiftPhotoGalleryBridge;

impl SwiftPhotoGalleryBridge {
    pub fn new() -> Self {
        SwiftPhotoGalleryBridge
    }

    // 获取最后的错误代码
    pub fn get_last_error_code(&self) -> PhotoGalleryError {
        let error_code = unsafe { get_last_error_code() };
        match error_code {
            0 => PhotoGalleryError::None,
            1 => PhotoGalleryError::PermissionDenied,
            2 => PhotoGalleryError::AssetNotFound,
            3 => PhotoGalleryError::InvalidFormat,
            4 => PhotoGalleryError::MemoryError,
            _ => PhotoGalleryError::Unknown,
        }
    }

    // 获取最后的错误消息
    pub fn get_last_error_message(&self) -> Option<String> {
        let msg_ptr = unsafe { get_last_error_message() };
        if msg_ptr.is_null() {
            return None;
        }
        
        let message = unsafe { CStr::from_ptr(msg_ptr).to_string_lossy().into_owned() };
        // 注意：这里需要手动释放消息字符串的内存
        // 但是由于 strdup 是在 Swift 端分配的，我们需要在 Swift 端提供释放函数
        Some(message)
    }

    pub fn request_permission(&self) -> Result<bool, PhotoGalleryError> {
        let granted = unsafe { request_photo_gallery_permission() };
        if granted {
            Ok(true)
        } else {
            Err(PhotoGalleryError::PermissionDenied)
        }
    }

    pub fn fetch_photo_assets(&self) -> Result<Vec<PhotoAssetInternal>, PhotoGalleryError> {
        let array_ptr = unsafe { fetch_photo_assets() };
        
        if array_ptr.is_null() {
            let error_code = self.get_last_error_code();
            let error_msg = self.get_last_error_message().unwrap_or_else(|| "Unknown error".to_string());
            log::error!("获取相片失败 (错误代码: {:?}): {}", error_code, error_msg);
            return Err(error_code);
        }

        let assets = unsafe {
            let array = &*array_ptr;
            std::slice::from_raw_parts(array.assets, array.count)
        };

        let mut result = Vec::new();
        for asset in assets {
            match PhotoAssetInternal::from_ffi(asset) {
                Ok(internal_asset) => result.push(internal_asset),
                Err(e) => {
                    log::error!("转换相片数据失败: {:?}", e);
                    continue;
                }
            }
        }

        unsafe { free_photo_asset_array(array_ptr); }
        Ok(result)
    }

    pub fn generate_thumbnail(&self, asset_id: &str, width: u32, height: u32) -> Result<ThumbnailDataInternal, PhotoGalleryError> {
        let asset_id_cstr = CString::new(asset_id)
            .map_err(|_| PhotoGalleryError::InvalidFormat)?;

        let thumbnail_ptr = unsafe {
            generate_thumbnail(asset_id_cstr.as_ptr(), width, height)
        };

        if thumbnail_ptr.is_null() {
            let error_code = self.get_last_error_code();
            let error_msg = self.get_last_error_message().unwrap_or_else(|| "Unknown error".to_string());
            log::error!("生成缩略图失败 (错误代码: {:?}): {}", error_code, error_msg);
            return Err(error_code);
        }

        let thumbnail_data = unsafe {
            ThumbnailDataInternal::from_ffi(&*thumbnail_ptr)?
        };

        unsafe { free_thumbnail_data(thumbnail_ptr); }
        Ok(thumbnail_data)
    }

    pub fn get_photo_info(&self, asset_id: &str) -> Result<PhotoInfoInternal, PhotoGalleryError> {
        let asset_id_cstr = CString::new(asset_id)
            .map_err(|_| PhotoGalleryError::InvalidFormat)?;

        let info_ptr = unsafe { get_photo_info(asset_id_cstr.as_ptr()) };

        if info_ptr.is_null() {
            let error_code = self.get_last_error_code();
            let error_msg = self.get_last_error_message().unwrap_or_else(|| "Unknown error".to_string());
            log::error!("获取相片信息失败 (错误代码: {:?}): {}", error_code, error_msg);
            return Err(error_code);
        }

        let photo_info = unsafe {
            PhotoInfoInternal::from_ffi(&*info_ptr)?
        };

        unsafe { free_photo_info(info_ptr); }
        Ok(photo_info)
    }
}

impl PhotoGalleryManager for SwiftPhotoGalleryBridge {
    fn request_permission() -> Result<bool, PhotoGalleryError> {
        let bridge = SwiftPhotoGalleryBridge::new();
        bridge.request_permission()
    }

    fn fetch_photo_assets() -> Result<Vec<PhotoAssetInternal>, PhotoGalleryError> {
        let bridge = SwiftPhotoGalleryBridge::new();
        bridge.fetch_photo_assets()
    }

    fn generate_thumbnail(asset_id: &str, width: u32, height: u32) -> Result<ThumbnailDataInternal, PhotoGalleryError> {
        let bridge = SwiftPhotoGalleryBridge::new();
        bridge.generate_thumbnail(asset_id, width, height)
    }

    fn get_photo_info(asset_id: &str) -> Result<PhotoInfoInternal, PhotoGalleryError> {
        let bridge = SwiftPhotoGalleryBridge::new();
        bridge.get_photo_info(asset_id)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_photo_asset_conversion() {
        let asset_id = CString::new("test_asset_id").unwrap();
        let asset = PhotoAsset {
            identifier: asset_id.as_ptr(),
            creation_date: 1640995200, // 2022-01-01 00:00:00
            modification_date: 1640995260,
            media_type: MediaType::Image,
            has_location: true,
            latitude: 39.9042,
            longitude: 116.4074,
        };

        let result = PhotoAssetInternal::from_ffi(&asset);
        assert!(result.is_ok());
        
        let internal = result.unwrap();
        assert_eq!(internal.id, "test_asset_id");
        assert_eq!(internal.creation_date, 1640995200);
        assert_eq!(internal.media_type, MediaType::Image);
        assert!(internal.has_location);
        assert_eq!(internal.latitude, Some(39.9042));
        assert_eq!(internal.longitude, Some(116.4074));
    }

    #[test]
    fn test_photo_info_conversion() {
        let file_name = CString::new("test_image.jpg").unwrap();
        let info = PhotoInfo {
            file_name: file_name.as_ptr(),
            file_size: 1024000,
            width: 1920,
            height: 1080,
            format: ImageFormat::JPEG,
            creation_date: 1640995200,
        };

        let result = PhotoInfoInternal::from_ffi(&info);
        assert!(result.is_ok());
        
        let internal = result.unwrap();
        assert_eq!(internal.file_name, "test_image.jpg");
        assert_eq!(internal.file_size, 1024000);
        assert_eq!(internal.width, 1920);
        assert_eq!(internal.height, 1080);
        assert_eq!(internal.format, ImageFormat::JPEG);
    }
}