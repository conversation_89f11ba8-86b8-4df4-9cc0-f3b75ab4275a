// 模块声明
mod models;
mod swift_bridge;
mod cache;
mod error;
mod commands;

// 重新导出常用类型和函数
pub use models::*;
pub use swift_bridge::SwiftPhotoGalleryBridge;
pub use commands::AppState;

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .manage(AppState::new())
        .invoke_handler(tauri::generate_handler![
            commands::request_permission,
            commands::get_photo_assets,
            commands::get_thumbnail,
            commands::get_photo_info,
            commands::search_photos,
            commands::get_cache_stats,
            commands::clear_cache,
            commands::cleanup_expired_cache
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
