use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::fs;
use std::io::{self, Read, Write};
use std::time::{SystemTime, UNIX_EPOCH};
use serde::{Deserialize, Serialize};
use tokio::sync::RwLock;
use lru::LruCache;
use crate::models::*;

// 缓存配置
#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct CacheConfig {
    pub memory_cache_size: usize,      // 内存缓存大小（条目数）
    pub disk_cache_dir: PathBuf,       // 磁盘缓存目录
    pub disk_cache_ttl: u64,           // 磁盘缓存生存时间（秒）
    pub thumbnail_size: (u32, u32),    // 默认缩略图尺寸
}

impl Default for CacheConfig {
    fn default() -> Self {
        CacheConfig {
            memory_cache_size: 1000,    // 缓存 1000 个缩略图
            disk_cache_dir: PathBuf::from(".cache/thumbnails"),
            disk_cache_ttl: 86400,      // 24 小时
            thumbnail_size: (200, 200),  // 200x200 像素
        }
    }
}

// 缓存键
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Serialize, Deserialize)]
pub struct CacheKey {
    pub asset_id: String,
    pub width: u32,
    pub height: u32,
}

// 缓存条目信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheEntry {
    pub key: CacheKey,
    pub created_at: u64,
    pub access_count: u64,
    pub last_access: u64,
    pub size: usize,
}

// 缓存错误类型
#[derive(Debug, thiserror::Error)]
pub enum CacheError {
    #[error("IO 错误: {0}")]
    Io(#[from] io::Error),
    #[error("序列化错误: {0}")]
    Serialization(#[from] serde_json::Error),
    #[error("缓存未命中")]
    CacheMiss,
    #[error("缓存过期")]
    CacheExpired,
    #[error("内存分配失败")]
    MemoryAllocationFailed,
}

// 缓存管理器
pub struct CacheManager {
    config: CacheConfig,
    memory_cache: RwLock<LruCache<CacheKey, ThumbnailDataInternal>>,
    disk_cache_index: RwLock<HashMap<CacheKey, CacheEntry>>,
}

impl CacheManager {
    pub fn new(config: CacheConfig) -> Result<Self, CacheError> {
        // 创建磁盘缓存目录
        if !config.disk_cache_dir.exists() {
            fs::create_dir_all(&config.disk_cache_dir)?;
        }

        // 初始化内存缓存
        let memory_cache = LruCache::new(std::num::NonZeroUsize::new(config.memory_cache_size).unwrap());

        // 加载磁盘缓存索引
        let disk_cache_index = Self::load_disk_cache_index(&config.disk_cache_dir)?;

        Ok(CacheManager {
            config,
            memory_cache: RwLock::new(memory_cache),
            disk_cache_index: RwLock::new(disk_cache_index),
        })
    }

    // 获取缩略图
    pub async fn get_thumbnail(
        &self,
        asset_id: &str,
        size: (u32, u32)
    ) -> Result<ThumbnailDataInternal, CacheError> {
        let key = CacheKey {
            asset_id: asset_id.to_string(),
            width: size.0,
            height: size.1,
        };

        // 1. 检查内存缓存
        {
            let mut memory_cache = self.memory_cache.write().await;
            if let Some(thumbnail) = memory_cache.get(&key) {
                // 更新访问信息
                self.update_access_info(&key).await?;
                return Ok(thumbnail.clone());
            }
        }

        // 2. 检查磁盘缓存
        if let Ok(thumbnail) = self.get_from_disk_cache(&key).await {
            // 缓存到内存
            {
                let mut memory_cache = self.memory_cache.write().await;
                memory_cache.put(key.clone(), thumbnail.clone());
            }
            self.update_access_info(&key).await?;
            return Ok(thumbnail);
        }

        Err(CacheError::CacheMiss)
    }

    // 存储缩略图
    pub async fn put_thumbnail(
        &self,
        asset_id: &str,
        size: (u32, u32),
        thumbnail: ThumbnailDataInternal
    ) -> Result<(), CacheError> {
        let key = CacheKey {
            asset_id: asset_id.to_string(),
            width: size.0,
            height: size.1,
        };

        // 存储到内存缓存
        {
            let mut memory_cache = self.memory_cache.write().await;
            memory_cache.put(key.clone(), thumbnail.clone());
        }

        // 存储到磁盘缓存
        self.save_to_disk_cache(&key, &thumbnail).await?;

        // 创建缓存条目
        let entry = CacheEntry {
            key: key.clone(),
            created_at: Self::current_timestamp(),
            access_count: 1,
            last_access: Self::current_timestamp(),
            size: thumbnail.data.len(),
        };

        // 更新索引
        {
            let mut index = self.disk_cache_index.write().await;
            index.insert(key, entry);
            self.save_disk_cache_index(&index).await?;
        }

        Ok(())
    }

    // 清理过期缓存
    pub async fn cleanup_expired_cache(&self) -> Result<(), CacheError> {
        let now = Self::current_timestamp();
        let mut index = self.disk_cache_index.write().await;

        let expired_keys: Vec<CacheKey> = index
            .iter()
            .filter(|(_, entry)| now - entry.last_access > self.config.disk_cache_ttl)
            .map(|(key, _)| key.clone())
            .collect();

        for key in expired_keys {
            // 从索引中移除
            index.remove(&key);
            
            // 删除文件
            let file_path = self.get_disk_cache_path(&key);
            if file_path.exists() {
                fs::remove_file(file_path)?;
            }
        }

        self.save_disk_cache_index(&index).await?;
        Ok(())
    }

    // 获取缓存统计信息
    pub async fn get_stats(&self) -> CacheStats {
        let memory_cache = self.memory_cache.read().await;
        let index = self.disk_cache_index.read().await;

        CacheStats {
            memory_cache_size: memory_cache.len(),
            disk_cache_size: index.len(),
            total_memory_usage: memory_cache
                .iter()
                .map(|(_, thumbnail)| thumbnail.data.len())
                .sum(),
            total_disk_usage: index
                .values()
                .map(|entry| entry.size)
                .sum(),
        }
    }

    // 清除所有缓存
    pub async fn clear_all(&self) -> Result<(), CacheError> {
        // 清除内存缓存
        {
            let mut memory_cache = self.memory_cache.write().await;
            memory_cache.clear();
        }

        // 清除磁盘缓存
        {
            let mut index = self.disk_cache_index.write().await;
            index.clear();
            self.save_disk_cache_index(&index).await?;
        }

        // 删除磁盘缓存文件
        if self.config.disk_cache_dir.exists() {
            fs::remove_dir_all(&self.config.disk_cache_dir)?;
            fs::create_dir_all(&self.config.disk_cache_dir)?;
        }

        Ok(())
    }

    // 内部方法

    async fn get_from_disk_cache(&self, key: &CacheKey) -> Result<ThumbnailDataInternal, CacheError> {
        let file_path = self.get_disk_cache_path(key);
        
        if !file_path.exists() {
            return Err(CacheError::CacheMiss);
        }

        // 检查是否过期
        let index = self.disk_cache_index.read().await;
        if let Some(entry) = index.get(key) {
            let now = Self::current_timestamp();
            if now - entry.last_access > self.config.disk_cache_ttl {
                return Err(CacheError::CacheExpired);
            }
        } else {
            return Err(CacheError::CacheMiss);
        }
        drop(index);

        // 读取文件
        let mut file = fs::File::open(file_path)?;
        let mut data = Vec::new();
        file.read_to_end(&mut data)?;

        // 反序列化
        let thumbnail: ThumbnailDataInternal = serde_json::from_slice(&data)?;
        Ok(thumbnail)
    }

    async fn save_to_disk_cache(&self, key: &CacheKey, thumbnail: &ThumbnailDataInternal) -> Result<(), CacheError> {
        let file_path = self.get_disk_cache_path(key);
        
        // 序列化数据
        let data = serde_json::to_vec(thumbnail)?;
        
        // 写入文件
        let mut file = fs::File::create(file_path)?;
        file.write_all(&data)?;
        
        Ok(())
    }

    async fn update_access_info(&self, key: &CacheKey) -> Result<(), CacheError> {
        let mut index = self.disk_cache_index.write().await;
        
        if let Some(entry) = index.get_mut(key) {
            entry.access_count += 1;
            entry.last_access = Self::current_timestamp();
            self.save_disk_cache_index(&index).await?;
        }
        
        Ok(())
    }

    fn get_disk_cache_path(&self, key: &CacheKey) -> PathBuf {
        let filename = format!("{}_{}_{}.json", 
            key.asset_id.replace("/", "_").replace(":", "_"), 
            key.width, 
            key.height);
        self.config.disk_cache_dir.join(filename)
    }

    fn load_disk_cache_index(cache_dir: &Path) -> Result<HashMap<CacheKey, CacheEntry>, CacheError> {
        let index_path = cache_dir.join("index.json");
        
        if !index_path.exists() {
            return Ok(HashMap::new());
        }

        let file = fs::File::open(index_path)?;
        let index: HashMap<CacheKey, CacheEntry> = serde_json::from_reader(file)?;
        Ok(index)
    }

    async fn save_disk_cache_index(&self, index: &HashMap<CacheKey, CacheEntry>) -> Result<(), CacheError> {
        let index_path = self.config.disk_cache_dir.join("index.json");
        
        let file = fs::File::create(index_path)?;
        serde_json::to_writer(file, index)?;
        
        Ok(())
    }

    fn current_timestamp() -> u64 {
        SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs()
    }
}

// 缓存统计信息
#[derive(Debug, Clone, serde::Serialize)]
pub struct CacheStats {
    pub memory_cache_size: usize,
    pub disk_cache_size: usize,
    pub total_memory_usage: usize,
    pub total_disk_usage: usize,
}

impl CacheStats {
    pub fn memory_usage_mb(&self) -> f64 {
        self.total_memory_usage as f64 / (1024.0 * 1024.0)
    }

    pub fn disk_usage_mb(&self) -> f64 {
        self.total_disk_usage as f64 / (1024.0 * 1024.0)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::tempdir;

    #[tokio::test]
    async fn test_cache_manager() {
        let temp_dir = tempdir().unwrap();
        let config = CacheConfig {
            disk_cache_dir: temp_dir.path().to_path_buf(),
            memory_cache_size: 10,
            ..Default::default()
        };

        let cache = CacheManager::new(config).unwrap();

        // 测试存储和获取
        let thumbnail = ThumbnailDataInternal {
            data: vec![1, 2, 3, 4, 5],
            width: 100,
            height: 100,
        };

        cache.put_thumbnail("test_asset", (100, 100), thumbnail.clone()).await.unwrap();
        
        let retrieved = cache.get_thumbnail("test_asset", (100, 100)).await.unwrap();
        assert_eq!(retrieved.data, thumbnail.data);
        assert_eq!(retrieved.width, thumbnail.width);
        assert_eq!(retrieved.height, thumbnail.height);
    }

    #[tokio::test]
    async fn test_cache_miss() {
        let temp_dir = tempdir().unwrap();
        let config = CacheConfig {
            disk_cache_dir: temp_dir.path().to_path_buf(),
            ..Default::default()
        };

        let cache = CacheManager::new(config).unwrap();

        let result = cache.get_thumbnail("non_existent", (100, 100)).await;
        assert!(matches!(result, Err(CacheError::CacheMiss)));
    }
}