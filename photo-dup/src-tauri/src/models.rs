use std::ffi::{CStr, CString};
use std::os::raw::{c_char, c_void};
use base64::Engine;

// 媒体类型枚举
#[repr(C)]
#[derive(Debug, Clone, Copy, PartialEq, Eq, serde::Serialize, serde::Deserialize)]
pub enum MediaType {
    Unknown = 0,
    Image = 1,
    Video = 2,
    Audio = 3,
}

// 图像格式枚举
#[repr(C)]
#[derive(Debug, Clone, Copy, PartialEq, Eq, serde::Serialize, serde::Deserialize)]
pub enum ImageFormat {
    Unknown = 0,
    JPEG = 1,
    PNG = 2,
    HEIC = 3,
    RAW = 4,
}

// 相片资源结构体 - 对应 Swift 端的 PhotoAsset
#[repr(C)]
#[derive(Debug)]
pub struct PhotoAsset {
    pub identifier: *const c_char,
    pub creation_date: i64,
    pub modification_date: i64,
    pub media_type: MediaType,
    pub has_location: bool,
    pub latitude: f64,
    pub longitude: f64,
}

// 相片信息结构体 - 对应 Swift 端的 PhotoInfo
#[repr(C)]
#[derive(Debug)]
pub struct PhotoInfo {
    pub file_name: *const c_char,
    pub file_size: i64,
    pub width: u32,
    pub height: u32,
    pub format: ImageFormat,
    pub creation_date: i64,
}

// 缩略图数据结构体 - 对应 Swift 端的 ThumbnailData
#[repr(C)]
#[derive(Debug)]
pub struct ThumbnailData {
    pub data: *const u8,
    pub length: usize,
    pub width: u32,
    pub height: u32,
}

// 相片资源数组 - 对应 Swift 端的 PhotoAssetArray
#[repr(C)]
#[derive(Debug)]
pub struct PhotoAssetArray {
    pub assets: *const PhotoAsset,
    pub count: usize,
}

// 错误代码枚举
#[repr(C)]
#[derive(Debug, Clone, Copy, PartialEq, Eq, serde::Serialize, serde::Deserialize)]
pub enum PhotoGalleryError {
    None = 0,
    PermissionDenied = 1,
    AssetNotFound = 2,
    InvalidFormat = 3,
    MemoryError = 4,
    Unknown = 5,
}

// 相册管理器特征
pub trait PhotoGalleryManager {
    fn request_permission() -> Result<bool, PhotoGalleryError>;
    fn fetch_photo_assets() -> Result<Vec<PhotoAssetInternal>, PhotoGalleryError>;
    fn generate_thumbnail(asset_id: &str, width: u32, height: u32) -> Result<ThumbnailDataInternal, PhotoGalleryError>;
    fn get_photo_info(asset_id: &str) -> Result<PhotoInfoInternal, PhotoGalleryError>;
}

// 内部数据结构 - 用于 Rust 端处理
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct PhotoAssetInternal {
    pub id: String,
    pub file_name: Option<String>,
    pub file_size: Option<i64>,
    pub width: Option<u32>,
    pub height: Option<u32>,
    pub format: Option<ImageFormat>,
    pub creation_date: i64,
    pub modification_date: Option<i64>,
    pub media_type: MediaType,
    pub has_location: bool,
    pub latitude: Option<f64>,
    pub longitude: Option<f64>,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct PhotoInfoInternal {
    pub file_name: String,
    pub file_size: i64,
    pub width: u32,
    pub height: u32,
    pub format: ImageFormat,
    pub creation_date: i64,
    pub has_location: bool,
    pub latitude: Option<f64>,
    pub longitude: Option<f64>,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct ThumbnailDataInternal {
    pub data: Vec<u8>,
    pub width: u32,
    pub height: u32,
}

// 数据转换实现
impl PhotoAssetInternal {
    pub fn from_ffi(asset: &PhotoAsset) -> Result<Self, PhotoGalleryError> {
        let identifier = unsafe {
            if asset.identifier.is_null() {
                return Err(PhotoGalleryError::InvalidFormat);
            }
            CStr::from_ptr(asset.identifier).to_str()
                .map_err(|_| PhotoGalleryError::InvalidFormat)?
                .to_string()
        };

        let location = if asset.has_location {
            Some((asset.latitude, asset.longitude))
        } else {
            None
        };

        Ok(PhotoAssetInternal {
            id: identifier,
            file_name: None,
            file_size: None,
            width: None,
            height: None,
            format: None,
            creation_date: asset.creation_date,
            modification_date: Some(asset.modification_date),
            media_type: asset.media_type,
            has_location: asset.has_location,
            latitude: location.map(|(lat, _)| lat),
            longitude: location.map(|(_, lng)| lng),
        })
    }
}

impl PhotoInfoInternal {
    pub fn from_ffi(info: &PhotoInfo) -> Result<Self, PhotoGalleryError> {
        let file_name = unsafe {
            if info.file_name.is_null() {
                return Err(PhotoGalleryError::InvalidFormat);
            }
            CStr::from_ptr(info.file_name).to_str()
                .map_err(|_| PhotoGalleryError::InvalidFormat)?
                .to_string()
        };

        let location = if info.width > 0 && info.height > 0 {
            // 这里可以根据需要添加位置信息
            None
        } else {
            None
        };

        Ok(PhotoInfoInternal {
            file_name,
            file_size: info.file_size,
            width: info.width,
            height: info.height,
            format: info.format,
            creation_date: info.creation_date,
            has_location: location.is_some(),
            latitude: location.map(|(lat, _)| lat),
            longitude: location.map(|(_, lng)| lng),
        })
    }
}

impl ThumbnailDataInternal {
    pub fn from_ffi(data: &ThumbnailData) -> Result<Self, PhotoGalleryError> {
        if data.data.is_null() || data.length == 0 {
            return Err(PhotoGalleryError::InvalidFormat);
        }

        let buffer = unsafe {
            std::slice::from_raw_parts(data.data, data.length)
        }.to_vec();

        Ok(ThumbnailDataInternal {
            data: buffer,
            width: data.width,
            height: data.height,
        })
    }
}

// 转换为 JSON 可序列化的格式
#[derive(Debug, Clone, serde::Serialize)]
pub struct PhotoAssetJson {
    pub id: String,
    pub file_name: Option<String>,
    pub file_size: Option<i64>,
    pub width: Option<u32>,
    pub height: Option<u32>,
    pub format: Option<ImageFormat>,
    pub creation_date: i64,
    pub modification_date: Option<i64>,
    pub media_type: MediaType,
    pub has_location: bool,
    pub latitude: Option<f64>,
    pub longitude: Option<f64>,
}

#[derive(Debug, Clone, serde::Serialize)]
pub struct PhotoInfoJson {
    pub file_name: String,
    pub file_size: i64,
    pub width: u32,
    pub height: u32,
    pub format: ImageFormat,
    pub creation_date: i64,
    pub has_location: bool,
    pub latitude: Option<f64>,
    pub longitude: Option<f64>,
}

#[derive(Debug, Clone, serde::Serialize)]
pub struct ThumbnailDataJson {
    pub data: String, // Base64 encoded
    pub width: u32,
    pub height: u32,
}

impl From<PhotoAssetInternal> for PhotoAssetJson {
    fn from(internal: PhotoAssetInternal) -> Self {
        PhotoAssetJson {
            id: internal.id,
            file_name: internal.file_name,
            file_size: internal.file_size,
            width: internal.width,
            height: internal.height,
            format: internal.format,
            creation_date: internal.creation_date,
            modification_date: internal.modification_date,
            media_type: internal.media_type,
            has_location: internal.has_location,
            latitude: internal.latitude,
            longitude: internal.longitude,
        }
    }
}

impl From<PhotoInfoInternal> for PhotoInfoJson {
    fn from(internal: PhotoInfoInternal) -> Self {
        PhotoInfoJson {
            file_name: internal.file_name,
            file_size: internal.file_size,
            width: internal.width,
            height: internal.height,
            format: internal.format,
            creation_date: internal.creation_date,
            has_location: internal.has_location,
            latitude: internal.latitude,
            longitude: internal.longitude,
        }
    }
}

impl From<ThumbnailDataInternal> for ThumbnailDataJson {
    fn from(internal: ThumbnailDataInternal) -> Self {
        let base64_data = base64::engine::general_purpose::STANDARD.encode(&internal.data);
        ThumbnailDataJson {
            data: base64_data,
            width: internal.width,
            height: internal.height,
        }
    }
}

// 内存安全的 FFI 数据释放
pub unsafe fn free_photo_asset_array(array: *mut PhotoAssetArray) {
    if array.is_null() {
        return;
    }

    let array_ref = &*array;
    if !array_ref.assets.is_null() {
        let assets = std::slice::from_raw_parts(array_ref.assets, array_ref.count);
        for asset in assets {
            if !asset.identifier.is_null() {
                drop(CString::from_raw(asset.identifier as *mut c_char));
            }
        }
        drop(Vec::from_raw_parts(array_ref.assets as *mut PhotoAsset, array_ref.count, array_ref.count));
    }
    drop(Box::from_raw(array));
}

pub unsafe fn free_thumbnail_data(data: *mut ThumbnailData) {
    if data.is_null() {
        return;
    }

    let data_ref = &*data;
    if !data_ref.data.is_null() {
        drop(Vec::from_raw_parts(data_ref.data as *mut u8, data_ref.length, data_ref.length));
    }
    drop(Box::from_raw(data));
}

pub unsafe fn free_photo_info(info: *mut PhotoInfo) {
    if info.is_null() {
        return;
    }

    let info_ref = &*info;
    if !info_ref.file_name.is_null() {
        drop(CString::from_raw(info_ref.file_name as *mut c_char));
    }
    drop(Box::from_raw(info));
}