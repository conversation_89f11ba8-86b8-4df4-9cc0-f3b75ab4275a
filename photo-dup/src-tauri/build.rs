use std::env;
use std::path::Path;
use std::process::Command;

fn main() {
    // 编译 Swift 代码
    compile_swift_code();

    // 运行 Tauri 构建
    tauri_build::build();
}

fn compile_swift_code() {
    let out_dir = env::var("OUT_DIR").unwrap();
    let swift_dir = Path::new("swift");
    let target_dir = env::var("CARGO_TARGET_DIR").unwrap_or_else(|_| "target".to_string());
    let profile = env::var("PROFILE").unwrap_or_else(|_| "debug".to_string());

    // 创建输出目录
    std::fs::create_dir_all(&out_dir).unwrap();

    // 编译 Swift 库
    let status = Command::new("swift")
        .args(&[
            "build",
            "--package-path",
            swift_dir.to_str().unwrap(),
            "--configuration",
            "release",
        ])
        .status()
        .expect("Failed to execute swift build");

    if !status.success() {
        panic!("Swift build failed");
    }

    // 复制动态库到目标目录
    let swift_lib_path = swift_dir.join(".build/release/libPhotoGalleryBridge.dylib");
    let target_lib_path = Path::new(&target_dir)
        .join(&profile)
        .join("libPhotoGalleryBridge.dylib");

    if swift_lib_path.exists() {
        std::fs::copy(&swift_lib_path, &target_lib_path).expect("Failed to copy Swift library");
        println!(
            "cargo:warning=Copied Swift library to {}",
            target_lib_path.display()
        );
    }

    // 链接编译后的动态库
    println!(
        "cargo:rustc-link-search=native={}/.build/release",
        swift_dir.display()
    );
    println!("cargo:rustc-link-lib=dylib=PhotoGalleryBridge");

    // 设置 rpath 以便运行时能找到动态库
    println!("cargo:rustc-link-arg=-Wl,-rpath,@executable_path");
    println!("cargo:rustc-link-arg=-Wl,-rpath,@loader_path");

    // 链接 macOS 系统框架
    println!("cargo:rustc-link-lib=framework=Foundation");
    println!("cargo:rustc-link-lib=framework=Photos");
}
