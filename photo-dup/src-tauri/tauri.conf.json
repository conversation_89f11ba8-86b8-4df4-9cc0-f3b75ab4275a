{"$schema": "https://schema.tauri.app/config/2", "productName": "photo-dup", "version": "0.1.0", "identifier": "com.photo-dup.app", "build": {"beforeDevCommand": "pnpm dev", "devUrl": "http://localhost:1421", "beforeBuildCommand": "pnpm build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "photo-dup", "width": 800, "height": 600, "center": true, "visible": true, "focus": true, "alwaysOnTop": false, "resizable": true}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "macOS": {"entitlements": "entitlements.plist", "exceptionDomain": null, "frameworks": [], "providerShortName": null, "signingIdentity": null}}}