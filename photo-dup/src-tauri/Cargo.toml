[package]
name = "photo-dup"
version = "0.1.0"
description = "A Tauri App"
authors = ["you"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "photo_dup_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { version = "2", features = [] }
tauri-plugin-opener = "2"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
tokio = { version = "1.0", features = ["full"] }
libc = "0.2"
objc = "0.2"
swift-rs = "1.0"
thiserror = "1.0"
log = "0.4"
base64 = "0.22"
lru = "0.12"
tempfile = "3.8"
chrono = { version = "0.4", features = ["serde"] }
futures = "0.3"

