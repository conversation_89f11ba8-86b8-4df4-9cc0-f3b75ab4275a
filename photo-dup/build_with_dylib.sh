#!/bin/bash

# 构建包含动态库的完整应用包

set -e

echo "开始构建 photo-dup 应用..."

# 清理之前的构建
echo "清理之前的构建..."
rm -rf src-tauri/target/release/bundle

# 构建应用（不包含 DMG）
echo "构建 Tauri 应用..."
pnpm tauri build --bundles app

# 检查构建是否成功
if [ ! -d "src-tauri/target/release/bundle/macos/photo-dup.app" ]; then
    echo "错误: 应用构建失败"
    exit 1
fi

# 复制动态库到应用包
echo "复制 Swift 动态库到应用包..."
./src-tauri/copy_dylib.sh \
    "src-tauri/target/release/bundle/macos/photo-dup.app" \
    "src-tauri/target/release/libPhotoGalleryBridge.dylib"

# 验证动态库是否存在
if [ ! -f "src-tauri/target/release/bundle/macos/photo-dup.app/Contents/MacOS/libPhotoGalleryBridge.dylib" ]; then
    echo "错误: 动态库复制失败"
    exit 1
fi

echo "应用包构建完成！"

# 现在构建 DMG，但需要确保动态库被包含
echo "构建 DMG..."

# 创建一个临时的构建钩子
cat > src-tauri/build_hook.sh << 'EOF'
#!/bin/bash
# 在每次构建后自动复制动态库
APP_BUNDLE="$1"
if [ -d "$APP_BUNDLE" ] && [ -f "target/release/libPhotoGalleryBridge.dylib" ]; then
    echo "复制动态库到 $APP_BUNDLE"
    cp "target/release/libPhotoGalleryBridge.dylib" "$APP_BUNDLE/Contents/MacOS/"
fi
EOF

chmod +x src-tauri/build_hook.sh

# 构建 DMG
pnpm tauri build --bundles dmg

# 手动复制动态库到 DMG 构建的应用包（如果需要）
if [ -d "src-tauri/target/release/bundle/macos/photo-dup.app" ] && [ ! -f "src-tauri/target/release/bundle/macos/photo-dup.app/Contents/MacOS/libPhotoGalleryBridge.dylib" ]; then
    echo "DMG 构建后补充复制动态库..."
    ./src-tauri/copy_dylib.sh \
        "src-tauri/target/release/bundle/macos/photo-dup.app" \
        "src-tauri/target/release/libPhotoGalleryBridge.dylib"
fi

# 清理临时文件
rm -f src-tauri/build_hook.sh

echo "构建完成！"
echo "应用位置: src-tauri/target/release/bundle/macos/photo-dup.app"
echo "DMG 位置: src-tauri/target/release/bundle/dmg/photo-dup_0.1.0_aarch64.dmg"

# 验证 DMG 中的应用是否包含动态库
echo "验证 DMG 中的应用..."
hdiutil attach "src-tauri/target/release/bundle/dmg/photo-dup_0.1.0_aarch64.dmg" -quiet
if [ -f "/Volumes/photo-dup/photo-dup.app/Contents/MacOS/libPhotoGalleryBridge.dylib" ]; then
    echo "✅ DMG 中的应用包含动态库"
else
    echo "❌ DMG 中的应用缺少动态库"
fi
hdiutil detach "/Volumes/photo-dup" -quiet

# 询问是否启动应用
read -p "是否启动应用进行测试? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "启动应用..."
    open "src-tauri/target/release/bundle/macos/photo-dup.app"
fi

echo "完成！"
