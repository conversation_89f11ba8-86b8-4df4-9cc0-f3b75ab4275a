#!/bin/bash

# 快速构建和启动应用

set -e

echo "🚀 快速构建 photo-dup 应用..."

# 构建应用包
echo "📦 构建应用包..."
pnpm tauri build --bundles app

# 复制动态库
echo "📋 复制动态库..."
./src-tauri/copy_dylib.sh \
    "src-tauri/target/release/bundle/macos/photo-dup.app" \
    "src-tauri/target/release/libPhotoGalleryBridge.dylib"

# 验证动态库
if [ -f "src-tauri/target/release/bundle/macos/photo-dup.app/Contents/MacOS/libPhotoGalleryBridge.dylib" ]; then
    echo "✅ 动态库复制成功"
else
    echo "❌ 动态库复制失败"
    exit 1
fi

echo "🎉 构建完成！"

# 询问是否启动应用
read -p "是否启动应用? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🚀 启动应用..."
    open "src-tauri/target/release/bundle/macos/photo-dup.app"
    echo "✨ 应用已启动！"
fi

echo "📍 应用位置: src-tauri/target/release/bundle/macos/photo-dup.app"
