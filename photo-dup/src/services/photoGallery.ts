import { invoke } from '@tauri-apps/api/core';
import {
    PhotoAsset,
    PhotoInfo,
    PhotoGalleryService,
    SearchQuery
} from '../types';

// 缓存管理接口
interface CacheManager {
    getThumbnail(key: string): Promise<string | null>;
    setThumbnail(key: string, data: string): Promise<void>;
    clear(): Promise<void>;
    getStats(): Promise<any>;
}

// 本地缓存实现
class LocalCacheManager implements CacheManager {
    private readonly CACHE_PREFIX = 'photo_gallery_';
    private readonly THUMBNAIL_PREFIX = 'thumbnail_';

    async getThumbnail(key: string): Promise<string | null> {
        try {
            const cacheKey = this.THUMBNAIL_PREFIX + key;
            const cached = localStorage.getItem(cacheKey);
            return cached;
        } catch (error) {
            console.warn('获取缓存失败:', error);
            return null;
        }
    }

    async setThumbnail(key: string, data: string): Promise<void> {
        try {
            const cacheKey = this.THUMBNAIL_PREFIX + key;
            localStorage.setItem(cacheKey, data);
        } catch (error) {
            console.warn('设置缓存失败:', error);
        }
    }

    async clear(): Promise<void> {
        try {
            // 清除所有相册相关的缓存
            const keys = Object.keys(localStorage);
            for (const key of keys) {
                if (key.startsWith(this.CACHE_PREFIX) || key.startsWith(this.THUMBNAIL_PREFIX)) {
                    localStorage.removeItem(key);
                }
            }
        } catch (error) {
            console.warn('清除缓存失败:', error);
        }
    }

    async getStats(): Promise<any> {
        try {
            const keys = Object.keys(localStorage);
            let thumbnailCount = 0;
            let totalSize = 0;

            for (const key of keys) {
                if (key.startsWith(this.THUMBNAIL_PREFIX)) {
                    thumbnailCount++;
                    const value = localStorage.getItem(key);
                    if (value) {
                        totalSize += value.length;
                    }
                }
            }

            return {
                thumbnailCount,
                totalSize: totalSize / 1024, // KB
                totalSizeMB: totalSize / (1024 * 1024) // MB
            };
        } catch (error) {
            console.warn('获取缓存统计失败:', error);
            return { thumbnailCount: 0, totalSize: 0, totalSizeMB: 0 };
        }
    }
}

export class PhotoGalleryServiceImpl implements PhotoGalleryService {
    private cache: CacheManager;
    private permissionGranted: boolean = false;

    constructor() {
        this.cache = new LocalCacheManager();
    }

    async requestPermission(): Promise<boolean> {
        try {
            const granted = await invoke<boolean>('request_permission');
            this.permissionGranted = granted;

            if (!granted) {
                throw new Error('需要相册访问权限。请在系统设置 > 隐私与安全性 > 照片中允许此应用访问您的照片库。');
            }

            if (granted) {
                // 权限获取成功，可以预加载一些数据
                this.preloadData();
            }

            return granted;
        } catch (error) {
            console.error('请求权限失败:', error);
            this.permissionGranted = false;
            throw error;
        }
    }

    async getPhotoAssets(options?: {
        limit?: number;
        offset?: number;
        sortBy?: 'date' | 'name' | 'size';
        sortOrder?: 'asc' | 'desc';
    }): Promise<PhotoAsset[]> {
        try {
            // 检查权限
            if (!this.permissionGranted) {
                const hasPermission = await this.requestPermission();
                if (!hasPermission) {
                    throw new Error('需要相册访问权限');
                }
            }

            let assets = await invoke<any[]>('get_photo_assets');

            // 应用排序
            if (options?.sortBy) {
                assets = this.sortAssets(assets, options.sortBy, options.sortOrder || 'desc');
            }

            // 应用分页
            if (options?.offset !== undefined || options?.limit !== undefined) {
                const start = options.offset || 0;
                const end = options.limit ? start + options.limit : undefined;
                assets = assets.slice(start, end);
            }

            return assets.map(asset => this.mapAsset(asset));
        } catch (error) {
            console.error('获取相片失败:', error);
            throw new Error(`获取相片失败: ${error}`);
        }
    }

    async getPhotoInfo(assetId: string): Promise<PhotoInfo> {
        try {
            // 先尝试从缓存获取
            const cacheKey = `info_${assetId}`;
            const cached = await this.cache.getThumbnail(cacheKey);
            if (cached) {
                return JSON.parse(cached);
            }

            // 从后端获取
            const info = await invoke<any>('get_photo_info', { assetId });
            const photoInfo = this.mapPhotoInfo(info);

            // 缓存结果
            await this.cache.setThumbnail(cacheKey, JSON.stringify(photoInfo));

            return photoInfo;
        } catch (error) {
            console.error('获取相片信息失败:', error);
            throw new Error(`获取相片信息失败: ${error}`);
        }
    }

    async getThumbnail(assetId: string, width: number, height: number): Promise<string> {
        try {
            // 生成缓存键
            const cacheKey = `${assetId}_${width}x${height}`;

            // 先尝试从缓存获取
            const cached = await this.cache.getThumbnail(cacheKey);
            if (cached) {
                return cached;
            }

            // 从后端获取
            const thumbnailData = await invoke<string>('get_thumbnail', {
                assetId,
                width,
                height
            });

            let result: string;
            if (thumbnailData.startsWith('data:image')) {
                result = thumbnailData;
            } else {
                result = `data:image/jpeg;base64,${thumbnailData}`;
            }

            // 缓存结果
            await this.cache.setThumbnail(cacheKey, result);

            return result;
        } catch (error) {
            console.error('获取缩略图失败:', error);
            throw new Error(`获取缩略图失败: ${error}`);
        }
    }

    async searchPhotos(query: SearchQuery): Promise<PhotoAsset[]> {
        try {
            // 检查权限
            if (!this.permissionGranted) {
                const hasPermission = await this.requestPermission();
                if (!hasPermission) {
                    throw new Error('需要相册访问权限');
                }
            }

            // 使用后端搜索
            const results = await invoke<PhotoAsset[]>('search_photos', {
                query: query.query
            });

            return results.map(asset => this.mapAsset(asset));
        } catch (error) {
            console.error('搜索相片失败:', error);
            // 如果后端搜索失败，回退到前端搜索
            return this.frontendSearch(query);
        }
    }

    async getCacheStats(): Promise<any> {
        return await this.cache.getStats();
    }

    async clearCache(): Promise<void> {
        return await this.cache.clear();
    }

    // 工具方法

    private mapAsset(asset: any): PhotoAsset {
        return {
            id: asset.identifier,
            fileName: asset.file_name,
            fileSize: asset.file_size,
            width: asset.width,
            height: asset.height,
            format: asset.format,
            creationDate: new Date(asset.creation_date * 1000),
            modificationDate: new Date(asset.modification_date * 1000),
            mediaType: asset.media_type,
            hasLocation: asset.has_location,
            latitude: asset.latitude,
            longitude: asset.longitude,
        };
    }

    private mapPhotoInfo(info: any): PhotoInfo {
        return {
            fileName: info.file_name,
            fileSize: info.file_size,
            dimensions: {
                width: info.width,
                height: info.height
            },
            format: info.format,
            creationDate: new Date(info.creation_date * 1000),
            location: info.has_location ? {
                latitude: info.latitude,
                longitude: info.longitude
            } : undefined
        };
    }

    private sortAssets(assets: any[], sortBy: string, sortOrder: 'asc' | 'desc'): any[] {
        return assets.sort((a, b) => {
            let comparison = 0;

            switch (sortBy) {
                case 'date':
                    comparison = a.creation_date - b.creation_date;
                    break;
                case 'name':
                    comparison = (a.file_name || '').localeCompare(b.file_name || '');
                    break;
                case 'size':
                    comparison = (a.file_size || 0) - (b.file_size || 0);
                    break;
                default:
                    comparison = 0;
            }

            return sortOrder === 'asc' ? comparison : -comparison;
        });
    }

    private async frontendSearch(query: SearchQuery): Promise<PhotoAsset[]> {
        const allPhotos = await this.getPhotoAssets();
        const lowerQuery = query.query.toLowerCase();

        return allPhotos.filter(photo => {
            // 文件名匹配
            if (photo.fileName?.toLowerCase().includes(lowerQuery)) {
                return true;
            }

            // ID 匹配
            if (photo.id.toLowerCase().includes(lowerQuery)) {
                return true;
            }

            // 日期范围过滤
            if (query.startDate) {
                if (new Date(photo.creationDate) < query.startDate) {
                    return false;
                }
            }

            if (query.endDate) {
                if (new Date(photo.creationDate) > query.endDate) {
                    return false;
                }
            }

            // 文件大小过滤
            if (query.minSize && photo.fileSize) {
                if (photo.fileSize < query.minSize) {
                    return false;
                }
            }

            if (query.maxSize && photo.fileSize) {
                if (photo.fileSize > query.maxSize) {
                    return false;
                }
            }

            // 媒体类型过滤
            if (query.mediaTypes && query.mediaTypes.length > 0) {
                if (!query.mediaTypes.includes(photo.mediaType)) {
                    return false;
                }
            }

            return true;
        });
    }

    private async preloadData(): Promise<void> {
        try {
            // 预加载前几张相片的缩略图
            const photos = await this.getPhotoAssets({ limit: 10 });

            // 并行预加载缩略图
            await Promise.all(
                photos.slice(0, 5).map(photo =>
                    this.getThumbnail(photo.id, 200, 200).catch(error => {
                        console.warn('预加载缩略图失败:', error);
                    })
                )
            );
        } catch (error) {
            console.warn('预加载数据失败:', error);
        }
    }
}

// 增强的服务接口
export interface EnhancedPhotoGalleryService extends PhotoGalleryService {
    getPhotoAssets(options?: {
        limit?: number;
        offset?: number;
        sortBy?: 'date' | 'name' | 'size';
        sortOrder?: 'asc' | 'desc';
    }): Promise<PhotoAsset[]>;
    searchPhotos(query: SearchQuery): Promise<PhotoAsset[]>;
    getCacheStats(): Promise<any>;
    clearCache(): Promise<void>;
}

// 导出增强的服务实例
export const photoGalleryService = new PhotoGalleryServiceImpl() as EnhancedPhotoGalleryService;

// 便捷方法
export const usePhotoGallery = () => {
    return {
        requestPermission: () => photoGalleryService.requestPermission(),
        getPhotoAssets: (options?: any) => photoGalleryService.getPhotoAssets(options),
        getPhotoInfo: (assetId: string) => photoGalleryService.getPhotoInfo(assetId),
        getThumbnail: (assetId: string, width: number, height: number) =>
            photoGalleryService.getThumbnail(assetId, width, height),
        searchPhotos: (query: SearchQuery) => photoGalleryService.searchPhotos(query),
        getCacheStats: () => photoGalleryService.getCacheStats(),
        clearCache: () => photoGalleryService.clearCache(),
    };
};