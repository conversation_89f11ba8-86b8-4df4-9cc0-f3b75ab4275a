// 媒体类型枚举
export enum MediaType {
  Unknown = 0,
  Image = 1,
  Video = 2,
  Audio = 3
}

// 图像格式枚举
export enum ImageFormat {
  Unknown = 0,
  JPEG = 1,
  PNG = 2,
  HEIC = 3,
  RAW = 4
}

// 相片资源接口
export interface PhotoAsset {
  id: string;
  fileName?: string;
  fileSize?: number;
  width?: number;
  height?: number;
  format?: ImageFormat;
  creationDate: Date;
  modificationDate?: Date;
  mediaType: MediaType;
  hasLocation?: boolean;
  latitude?: number;
  longitude?: number;
  thumbnail?: string;
}

// 相片信息接口
export interface PhotoInfo {
  fileName: string;
  fileSize: number;
  dimensions: {
    width: number;
    height: number;
  };
  format: ImageFormat;
  creationDate: Date;
  location?: {
    latitude: number;
    longitude: number;
  };
}

// 缩略图请求参数
export interface ThumbnailRequest {
  assetId: string;
  width: number;
  height: number;
}

// 搜索查询参数
export interface SearchQuery {
  query: string;
  startDate?: Date;
  endDate?: Date;
  mediaTypes?: MediaType[];
  minSize?: number;
  maxSize?: number;
}

// 相册服务接口
export interface PhotoGalleryService {
  // 权限管理
  requestPermission(): Promise<boolean>;
  
  // 相片获取
  getPhotoAssets(): Promise<PhotoAsset[]>;
  getPhotoInfo(assetId: string): Promise<PhotoInfo>;
  
  // 缩略图获取
  getThumbnail(assetId: string, width: number, height: number): Promise<string>;
  
  // 搜索功能
  searchPhotos(query: SearchQuery): Promise<PhotoAsset[]>;
}

// 组件 Props 类型
export interface PhotoGalleryProps {
  photos: PhotoAsset[];
  photosPerRow: number;
  onPhotoSelect?: (photo: PhotoAsset) => void;
  onPhotoClick?: (photo: PhotoAsset) => void;
  loading?: boolean;
  className?: string;
}

export interface PhotoThumbnailProps {
  photo: PhotoAsset;
  selected?: boolean;
  onSelect?: (photo: PhotoAsset) => void;
  onClick?: (photo: PhotoAsset) => void;
  viewMode?: 'grid' | 'list';
  className?: string;
}

// 应用状态接口
export interface AppState {
  photos: PhotoAsset[];
  selectedPhotos: PhotoAsset[];
  loading: boolean;
  error: string | null;
  photosPerRow: number;
  hasPermission: boolean | null;
}