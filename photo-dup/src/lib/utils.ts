import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
import { ImageFormat } from "@/types"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

export function getImageFormat(format: ImageFormat): string {
  switch (format) {
    case ImageFormat.JPEG: return 'JPEG'
    case ImageFormat.PNG: return 'PNG'
    case ImageFormat.HEIC: return 'HEIC'
    case ImageFormat.RAW: return 'RAW'
    default: return 'Unknown'
  }
}