import React, { useState, useEffect, useCallback } from 'react';
import { PhotoGalleryProps, PhotoAsset } from '@/types';
import { PhotoThumbnail } from './PhotoThumbnail';
import { Loading } from '@/components/ui/loading';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search, RefreshCw, Grid3x3, List, Settings } from 'lucide-react';
import { cn } from '@/lib/utils';
import { photoGalleryService } from '@/services/photoGallery';

export const PhotoGallery: React.FC<PhotoGalleryProps> = ({
  photos,
  photosPerRow = 6,
  onPhotoSelect,
  onPhotoClick,
  loading = false,
  className
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredPhotos, setFilteredPhotos] = useState<PhotoAsset[]>(photos);
  const [selectedPhotos, setSelectedPhotos] = useState<PhotoAsset[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // 过滤相片
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredPhotos(photos);
    } else {
      const query = searchQuery.toLowerCase();
      const filtered = photos.filter(photo =>
        photo.fileName?.toLowerCase().includes(query) ||
        photo.id.toLowerCase().includes(query)
      );
      setFilteredPhotos(filtered);
    }
  }, [photos, searchQuery]);

  // 处理相片选择
  const handlePhotoSelect = useCallback((photo: PhotoAsset) => {
    setSelectedPhotos(prev => {
      const isSelected = prev.some(p => p.id === photo.id);
      if (isSelected) {
        return prev.filter(p => p.id !== photo.id);
      } else {
        return [...prev, photo];
      }
    });
    onPhotoSelect?.(photo);
  }, [onPhotoSelect]);

  // 处理相片点击
  const handlePhotoClick = useCallback((photo: PhotoAsset) => {
    onPhotoClick?.(photo);
  }, [onPhotoClick]);

  // 选择所有相片
  const handleSelectAll = () => {
    if (selectedPhotos.length === filteredPhotos.length) {
      setSelectedPhotos([]);
    } else {
      setSelectedPhotos(filteredPhotos);
    }
  };

  // 刷新相片
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      // 这里可以调用刷新函数
      // const refreshedPhotos = await photoGalleryService.getPhotoAssets();
      // setFilteredPhotos(refreshedPhotos);
      setSelectedPhotos([]);
    } catch (error) {
      console.error('刷新相片失败:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  // 计算网格布局
  const gridStyle = {
    gridTemplateColumns: `repeat(${photosPerRow}, minmax(0, 1fr))`,
  };

  if (loading) {
    return (
      <div className={cn("flex items-center justify-center h-96", className)}>
        <Loading text="加载相片中..." size="lg" />
      </div>
    );
  }

  return (
    <div className={cn("w-full space-y-4", className)}>
      {/* 工具栏 */}
      <div className="flex items-center justify-between gap-4 p-4 bg-card rounded-lg border">
        <div className="flex items-center gap-2 flex-1">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="搜索相片..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleSelectAll}
            disabled={filteredPhotos.length === 0}
          >
            {selectedPhotos.length === filteredPhotos.length ? '取消全选' : '全选'}
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            <RefreshCw className={cn("h-4 w-4", isRefreshing && "animate-spin")} />
          </Button>
          
          <div className="flex border rounded-md">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              className="rounded-r-none"
              onClick={() => setViewMode('grid')}
            >
              <Grid3x3 className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              className="rounded-l-none"
              onClick={() => setViewMode('list')}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
          
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* 状态栏 */}
      <div className="flex items-center justify-between text-sm text-muted-foreground">
        <span>
          共 {filteredPhotos.length} 张相片
          {selectedPhotos.length > 0 && `，已选择 ${selectedPhotos.length} 张`}
        </span>
        {selectedPhotos.length > 0 && (
          <Button
            variant="destructive"
            size="sm"
            onClick={() => setSelectedPhotos([])}
          >
            清除选择
          </Button>
        )}
      </div>

      {/* 相片网格 */}
      {filteredPhotos.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-96 text-muted-foreground border-2 border-dashed border-muted-foreground/25 rounded-lg">
          <Search className="h-12 w-12 mb-4 opacity-50" />
          <h3 className="text-lg font-medium mb-2">没有找到相片</h3>
          <p className="text-sm">
            {searchQuery ? '尝试使用其他关键词搜索' : '请检查相册访问权限'}
          </p>
        </div>
      ) : (
        <div
          className={cn(
            "grid gap-4 p-4",
            viewMode === 'grid' ? "" : "grid-cols-1"
          )}
          style={viewMode === 'grid' ? gridStyle : undefined}
        >
          {filteredPhotos.map((photo) => (
            <PhotoThumbnail
              key={photo.id}
              photo={photo}
              selected={selectedPhotos.some(p => p.id === photo.id)}
              onSelect={handlePhotoSelect}
              onClick={handlePhotoClick}
              viewMode={viewMode}
            />
          ))}
        </div>
      )}
    </div>
  );
};

// 相册容器组件
export const PhotoGalleryContainer: React.FC = () => {
  const [photos, setPhotos] = useState<PhotoAsset[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const photosPerRow = 6;

  useEffect(() => {
    loadPhotos();
  }, []);

  const loadPhotos = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // 请求权限
      const hasPermission = await photoGalleryService.requestPermission();
      if (!hasPermission) {
        setError('需要相册访问权限，请在系统设置中允许应用访问相册');
        return;
      }

      // 加载相片
      const photoAssets = await photoGalleryService.getPhotoAssets();
      setPhotos(photoAssets);
    } catch (err) {
      console.error('加载相片失败:', err);
      setError(err instanceof Error ? err.message : '加载相片失败');
    } finally {
      setLoading(false);
    }
  };

  const handlePhotoSelect = (photo: PhotoAsset) => {
    console.log('选择相片:', photo.fileName || photo.id);
  };

  const handlePhotoClick = (photo: PhotoAsset) => {
    console.log('点击相片:', photo.fileName || photo.id);
    // 这里可以打开大图预览
  };

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-96 text-center">
        <div className="text-destructive mb-4">
          <svg className="h-12 w-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <h3 className="text-lg font-medium mb-2">无法访问相册</h3>
        <p className="text-muted-foreground mb-4 max-w-md">{error}</p>
        <Button onClick={loadPhotos} variant="outline">
          重试
        </Button>
      </div>
    );
  }

  return (
    <div className="w-full h-full">
      <PhotoGallery
        photos={photos}
        photosPerRow={photosPerRow}
        onPhotoSelect={handlePhotoSelect}
        onPhotoClick={handlePhotoClick}
        loading={loading}
        className="h-full"
      />
    </div>
  );
};