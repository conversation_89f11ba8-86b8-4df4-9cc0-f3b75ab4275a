import React, { useState, useEffect, useCallback } from 'react';
import { PhotoThumbnailProps, PhotoAsset, MediaType, ImageFormat } from '@/types';
import { Loading } from '@/components/ui/loading';
import { Button } from '@/components/ui/button';
import { Check, Eye, Image as ImageIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { photoGalleryService } from '@/services/photoGallery';
import { formatDate, formatFileSize, getImageFormat } from '@/lib/utils';

export const PhotoThumbnail: React.FC<PhotoThumbnailProps> = ({
  photo,
  selected = false,
  onSelect,
  onClick,
  viewMode = 'grid',
  className
}) => {
  const [thumbnail, setThumbnail] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const [hovered, setHovered] = useState(false);

  // 加载缩略图
  const loadThumbnail = useCallback(async () => {
    if (!photo.id) return;

    setLoading(true);
    setError(false);
    
    try {
      // 从本地存储或服务获取缩略图
      const thumbnailData = await photoGalleryService.getThumbnail(
        photo.id, 
        200, 
        200
      );
      setThumbnail(thumbnailData);
    } catch (err) {
      console.error('加载缩略图失败:', err);
      setError(true);
    } finally {
      setLoading(false);
    }
  }, [photo.id]);

  useEffect(() => {
    loadThumbnail();
  }, [loadThumbnail]);

  // 处理点击事件
  const handleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onClick?.(photo);
  }, [photo, onClick]);

  // 处理选择事件
  const handleSelect = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onSelect?.(photo);
  }, [photo, onSelect]);

  // 获取相片格式文本
  const getFormatText = (format?: ImageFormat): string => {
    return getImageFormat(format || ImageFormat.Unknown);
  };

  // 获取媒体类型图标
  const getMediaTypeIcon = (mediaType: MediaType) => {
    switch (mediaType) {
      case MediaType.Image:
        return <ImageIcon className="h-4 w-4" />;
      case MediaType.Video:
        return <div className="text-xs">▶</div>;
      case MediaType.Audio:
        return <div className="text-xs">♪</div>;
      default:
        return <ImageIcon className="h-4 w-4" />;
    }
  };

  if (viewMode === 'list') {
    return (
      <div
        className={cn(
          "flex items-center gap-4 p-4 border rounded-lg cursor-pointer transition-all hover:shadow-md",
          selected && "border-primary bg-primary/5",
          className
        )}
        onClick={handleClick}
        onMouseEnter={() => setHovered(true)}
        onMouseLeave={() => setHovered(false)}
      >
        {/* 缩略图 */}
        <div className="relative w-16 h-16 bg-muted rounded flex-shrink-0 overflow-hidden">
          {loading && (
            <div className="absolute inset-0 flex items-center justify-center bg-muted/50">
              <Loading size="sm" />
            </div>
          )}
          {error && (
            <div className="absolute inset-0 flex items-center justify-center bg-muted/50">
              <ImageIcon className="h-6 w-6 text-muted-foreground" />
            </div>
          )}
          {thumbnail && (
            <img
              src={thumbnail}
              alt={photo.fileName || '相片'}
              className="w-full h-full object-cover"
              onLoad={() => setLoading(false)}
            />
          )}
        </div>

        {/* 相片信息 */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            {getMediaTypeIcon(photo.mediaType)}
            <h3 className="font-medium truncate">
              {photo.fileName || '未命名相片'}
            </h3>
          </div>
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <span>{getFormatText(photo.format)}</span>
            {photo.width && photo.height && (
              <span>{photo.width} × {photo.height}</span>
            )}
            {photo.fileSize && (
              <span>{formatFileSize(photo.fileSize)}</span>
            )}
            <span>{formatDate(photo.creationDate)}</span>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center gap-2">
          {hovered && (
            <>
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  handleClick(e);
                }}
              >
                <Eye className="h-4 w-4" />
              </Button>
            </>
          )}
          
          <div
            className={cn(
              "w-6 h-6 rounded border-2 flex items-center justify-center cursor-pointer transition-all",
              selected 
                ? "border-primary bg-primary text-primary-foreground" 
                : "border-muted-foreground/50 hover:border-primary"
            )}
            onClick={handleSelect}
          >
            {selected && <Check className="h-4 w-4" />}
          </div>
        </div>
      </div>
    );
  }

  // 网格视图
  return (
    <div
      className={cn(
        "relative group cursor-pointer overflow-hidden rounded-lg border transition-all",
        "aspect-square", // 保持正方形比例
        selected && "border-primary ring-2 ring-primary/20",
        "hover:shadow-lg hover:scale-105",
        loading && "bg-muted",
        error && "bg-muted/50",
        className
      )}
      onClick={handleClick}
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
    >
      {/* 加载状态 */}
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center bg-muted/50">
          <Loading size="md" />
        </div>
      )}

      {/* 错误状态 */}
      {error && (
        <div className="absolute inset-0 flex flex-col items-center justify-center bg-muted/50 text-muted-foreground">
          <ImageIcon className="h-8 w-8 mb-2" />
          <span className="text-xs">加载失败</span>
        </div>
      )}

      {/* 缩略图 */}
      {thumbnail && (
        <>
          <img
            src={thumbnail}
            alt={photo.fileName || '相片'}
            className="w-full h-full object-cover transition-transform group-hover:scale-105"
            onLoad={() => setLoading(false)}
          />
          
          {/* 悬停时的覆盖层 */}
          <div
            className={cn(
              "absolute inset-0 bg-black/50 opacity-0 transition-opacity",
              hovered && "opacity-100"
            )}
          >
            <div className="absolute inset-0 flex items-center justify-center">
              <Button
                variant="secondary"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  handleClick(e);
                }}
              >
                <Eye className="h-4 w-4 mr-1" />
                查看
              </Button>
            </div>
          </div>
        </>
      )}

      {/* 选择框 */}
      <div
        className={cn(
          "absolute top-2 right-2 w-6 h-6 rounded border-2 flex items-center justify-center cursor-pointer transition-all z-10",
          "bg-background/80 backdrop-blur-sm",
          selected 
            ? "border-primary bg-primary text-primary-foreground" 
            : "border-muted-foreground/50 hover:border-primary hover:bg-background"
        )}
        onClick={handleSelect}
      >
        {selected && <Check className="h-4 w-4" />}
      </div>

      {/* 底部信息条 */}
      {!loading && !error && (
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-2">
          <div className="flex items-center justify-between text-white text-xs">
            <span className="truncate">
              {photo.fileName || '未命名相片'}
            </span>
            {photo.width && photo.height && (
              <span>
                {photo.width}×{photo.height}
              </span>
            )}
          </div>
        </div>
      )}

      {/* 媒体类型标识 */}
      <div className="absolute top-2 left-2">
        <div className="bg-background/80 backdrop-blur-sm rounded p-1">
          {getMediaTypeIcon(photo.mediaType)}
        </div>
      </div>
    </div>
  );
};

// 缩略图网格组件
interface PhotoThumbnailGridProps {
  photos: PhotoAsset[];
  photosPerRow?: number;
  selectedPhotos?: PhotoAsset[];
  onPhotoSelect?: (photo: PhotoAsset) => void;
  onPhotoClick?: (photo: PhotoAsset) => void;
  loading?: boolean;
  className?: string;
}

export const PhotoThumbnailGrid: React.FC<PhotoThumbnailGridProps> = ({
  photos,
  photosPerRow = 6,
  selectedPhotos = [],
  onPhotoSelect,
  onPhotoClick,
  loading = false,
  className
}) => {
  const gridStyle = {
    gridTemplateColumns: `repeat(${photosPerRow}, minmax(0, 1fr))`,
  };

  if (loading) {
    return (
      <div className={cn("grid gap-4", className)} style={gridStyle}>
        {Array.from({ length: photosPerRow * 3 }).map((_, index) => (
          <div
            key={index}
            className="aspect-square bg-muted rounded-lg animate-pulse"
          />
        ))}
      </div>
    );
  }

  return (
    <div className={cn("grid gap-4", className)} style={gridStyle}>
      {photos.map((photo) => (
        <PhotoThumbnail
          key={photo.id}
          photo={photo}
          selected={selectedPhotos.some(p => p.id === photo.id)}
          onSelect={onPhotoSelect}
          onClick={onPhotoClick}
          className="w-full"
        />
      ))}
    </div>
  );
};