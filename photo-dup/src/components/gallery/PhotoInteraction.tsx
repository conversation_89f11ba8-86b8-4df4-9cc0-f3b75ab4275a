import React, { useState, useCallback, useEffect } from 'react';
import { PhotoAsset } from '@/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Search, 
  X, 
  Download, 
  Trash2, 
  Share2, 
  Info,
  Grid3x3,
  List,
  SlidersHorizontal,
  Calendar,
  Filter
} from 'lucide-react';
import { cn } from '@/lib/utils';

// 高级搜索过滤器
interface SearchFilters {
  query: string;
  startDate?: Date;
  endDate?: Date;
  minSize?: number;
  maxSize?: number;
  formats?: string[];
  sortBy: 'date' | 'name' | 'size' | 'type';
  sortOrder: 'asc' | 'desc';
}

interface PhotoInteractionProps {
  photos: PhotoAsset[];
  selectedPhotos: PhotoAsset[];
  onPhotoSelect: (photo: PhotoAsset) => void;
  onPhotoClick: (photo: PhotoAsset) => void;
  onSelectionChange?: (selected: PhotoAsset[]) => void;
  className?: string;
  children?: React.ReactNode;
}

export const PhotoInteraction: React.FC<PhotoInteractionProps> = ({
  photos,
  selectedPhotos,
  onPhotoSelect,
  onPhotoClick,
  onSelectionChange,
  className,
  children
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredPhotos, setFilteredPhotos] = useState<PhotoAsset[]>(photos);
  const [filters, setFilters] = useState<SearchFilters>({
    query: '',
    sortBy: 'date',
    sortOrder: 'desc'
  });
  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showSearchBar, setShowSearchBar] = useState(false);

  // 应用搜索和过滤
  useEffect(() => {
    let result = [...photos];

    // 文本搜索
    if (filters.query.trim()) {
      const query = filters.query.toLowerCase();
      result = result.filter(photo =>
        photo.fileName?.toLowerCase().includes(query) ||
        photo.id.toLowerCase().includes(query)
      );
    }

    // 日期过滤
    if (filters.startDate) {
      result = result.filter(photo => 
        new Date(photo.creationDate) >= filters.startDate!
      );
    }

    if (filters.endDate) {
      result = result.filter(photo => 
        new Date(photo.creationDate) <= filters.endDate!
      );
    }

    // 文件大小过滤
    if (filters.minSize) {
      result = result.filter(photo => 
        (photo.fileSize || 0) >= filters.minSize!
      );
    }

    if (filters.maxSize) {
      result = result.filter(photo => 
        (photo.fileSize || 0) <= filters.maxSize!
      );
    }

    // 格式过滤
    if (filters.formats && filters.formats.length > 0) {
      result = result.filter(photo =>
        filters.formats!.includes(photo.format?.toString() || '')
      );
    }

    // 排序
    result.sort((a, b) => {
      let comparison = 0;
      
      switch (filters.sortBy) {
        case 'date':
          comparison = new Date(a.creationDate).getTime() - new Date(b.creationDate).getTime();
          break;
        case 'name':
          comparison = (a.fileName || '').localeCompare(b.fileName || '');
          break;
        case 'size':
          comparison = (a.fileSize || 0) - (b.fileSize || 0);
          break;
        case 'type':
          comparison = (a.mediaType || 0) - (b.mediaType || 0);
          break;
      }

      return filters.sortOrder === 'asc' ? comparison : -comparison;
    });

    setFilteredPhotos(result);
  }, [photos, filters]);

  // 通知选择变化
  useEffect(() => {
    onSelectionChange?.(selectedPhotos);
  }, [selectedPhotos, onSelectionChange]);

  // 处理搜索
  const handleSearch = (query: string) => {
    setFilters(prev => ({ ...prev, query }));
    setSearchQuery(query);
  };

  // 清除搜索
  const clearSearch = () => {
    setSearchQuery('');
    setFilters(prev => ({ ...prev, query: '' }));
  };

  // 选择操作
  const handleSelectAll = () => {
    if (selectedPhotos.length === filteredPhotos.length) {
      // 取消全选
      filteredPhotos.forEach(photo => {
        if (selectedPhotos.some(p => p.id === photo.id)) {
          onPhotoSelect(photo);
        }
      });
    } else {
      // 全选
      filteredPhotos.forEach(photo => {
        if (!selectedPhotos.some(p => p.id === photo.id)) {
          onPhotoSelect(photo);
        }
      });
    }
  };

  // 清除选择
  const clearSelection = () => {
    selectedPhotos.forEach(photo => onPhotoSelect(photo));
  };

  // 批量操作
  const handleBatchOperation = (operation: 'delete' | 'download' | 'share') => {
    console.log(`批量${operation}:`, selectedPhotos.map(p => p.fileName || p.id));
    // 这里可以实现具体的批量操作逻辑
  };

  // 格式化文件大小
  const formatFileSize = (bytes?: number): string => {
    if (!bytes) return 'Unknown';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* 搜索和工具栏 */}
      <div className="flex items-center gap-2">
        {showSearchBar ? (
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="搜索相片..."
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-10 pr-10"
              autoFocus
            />
            {searchQuery && (
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                onClick={clearSearch}
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>
        ) : (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowSearchBar(true)}
            className="flex-1 justify-start"
          >
            <Search className="h-4 w-4 mr-2" />
            搜索相片...
          </Button>
        )}

        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowFilters(!showFilters)}
        >
          <Filter className="h-4 w-4" />
        </Button>

        <div className="flex border rounded-md">
          <Button
            variant={viewMode === 'grid' ? 'default' : 'ghost'}
            size="sm"
            className="rounded-r-none"
            onClick={() => setViewMode('grid')}
          >
            <Grid3x3 className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'ghost'}
            size="sm"
            className="rounded-l-none"
            onClick={() => setViewMode('list')}
          >
            <List className="h-4 w-4" />
          </Button>
        </div>

        <Button
          variant="outline"
          size="sm"
          onClick={handleSelectAll}
          disabled={filteredPhotos.length === 0}
        >
          {selectedPhotos.length === filteredPhotos.length ? '取消全选' : '全选'}
        </Button>
      </div>

      {/* 高级过滤器 */}
      {showFilters && (
        <div className="p-4 bg-card rounded-lg border space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="font-medium flex items-center gap-2">
              <SlidersHorizontal className="h-4 w-4" />
              高级过滤器
            </h3>
            <Button variant="ghost" size="sm" onClick={() => setShowFilters(false)}>
              <X className="h-4 w-4" />
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* 排序选项 */}
            <div>
              <label className="text-sm font-medium mb-2 block">排序方式</label>
              <select
                value={filters.sortBy}
                onChange={(e) => setFilters(prev => ({ 
                  ...prev, 
                  sortBy: e.target.value as SearchFilters['sortBy'] 
                }))}
                className="w-full p-2 border rounded-md"
              >
                <option value="date">日期</option>
                <option value="name">名称</option>
                <option value="size">大小</option>
                <option value="type">类型</option>
              </select>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">排序顺序</label>
              <select
                value={filters.sortOrder}
                onChange={(e) => setFilters(prev => ({ 
                  ...prev, 
                  sortOrder: e.target.value as SearchFilters['sortOrder'] 
                }))}
                className="w-full p-2 border rounded-md"
              >
                <option value="desc">降序</option>
                <option value="asc">升序</option>
              </select>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">文件格式</label>
              <div className="space-y-2">
                {['JPEG', 'PNG', 'HEIC', 'RAW'].map(format => (
                  <label key={format} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={filters.formats?.includes(format) || false}
                      onChange={(e) => {
                        const formats = filters.formats || [];
                        if (e.target.checked) {
                          setFilters(prev => ({ 
                            ...prev, 
                            formats: [...formats, format] 
                          }));
                        } else {
                          setFilters(prev => ({ 
                            ...prev, 
                            formats: formats.filter(f => f !== format) 
                          }));
                        }
                      }}
                    />
                    <span className="text-sm">{format}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>

          {/* 日期范围 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                开始日期
              </label>
              <Input
                type="date"
                onChange={(e) => setFilters(prev => ({ 
                  ...prev, 
                  startDate: e.target.value ? new Date(e.target.value) : undefined 
                }))}
              />
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                结束日期
              </label>
              <Input
                type="date"
                onChange={(e) => setFilters(prev => ({ 
                  ...prev, 
                  endDate: e.target.value ? new Date(e.target.value) : undefined 
                }))}
              />
            </div>
          </div>
        </div>
      )}

      {/* 状态栏 */}
      <div className="flex items-center justify-between text-sm">
        <span className="text-muted-foreground">
          共 {filteredPhotos.length} 张相片
          {selectedPhotos.length > 0 && `，已选择 ${selectedPhotos.length} 张`}
        </span>

        {selectedPhotos.length > 0 && (
          <div className="flex items-center gap-2">
            <span className="text-muted-foreground">
              总大小: {formatFileSize(
                selectedPhotos.reduce((sum, photo) => sum + (photo.fileSize || 0), 0)
              )}
            </span>
            <div className="flex border rounded-md">
              <Button
                variant="ghost"
                size="sm"
                className="rounded-r-none"
                onClick={() => handleBatchOperation('download')}
                title="下载"
              >
                <Download className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="rounded-none border-x"
                onClick={() => handleBatchOperation('share')}
                title="分享"
              >
                <Share2 className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="rounded-l-none text-destructive hover:text-destructive"
                onClick={() => handleBatchOperation('delete')}
                title="删除"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={clearSelection}
            >
              清除选择
            </Button>
          </div>
        )}
      </div>

      {/* 交互提示 */}
      {selectedPhotos.length === 1 && (
        <div className="flex items-center gap-4 p-3 bg-muted rounded-lg text-sm">
          <Info className="h-4 w-4 text-muted-foreground" />
          <span>点击相片查看大图，点击选择框进行选择</span>
        </div>
      )}

      {/* 传递给父组件的渲染内容 */}
      {children && React.Children.map(children, child =>
        React.cloneElement(child as React.ReactElement, {
          photos: filteredPhotos,
          viewMode,
          selectedPhotos,
          onPhotoSelect,
          onPhotoClick
        })
      )}
    </div>
  );
};

// 交互包装器组件
interface PhotoInteractionWrapperProps {
  photos: PhotoAsset[];
  photosPerRow?: number;
  onPhotoSelect?: (photo: PhotoAsset) => void;
  onPhotoClick?: (photo: PhotoAsset) => void;
  onSelectionChange?: (selected: PhotoAsset[]) => void;
  className?: string;
  children?: React.ReactNode;
}

export const PhotoInteractionWrapper: React.FC<PhotoInteractionWrapperProps> = ({
  photos,
  photosPerRow = 6,
  onPhotoSelect,
  onPhotoClick,
  onSelectionChange,
  className,
  children
}) => {
  const [selectedPhotos, setSelectedPhotos] = useState<PhotoAsset[]>([]);
  
  // 确保 photosPerRow 被使用，避免未使用变量的警告
  void photosPerRow;

  const handlePhotoSelect = useCallback((photo: PhotoAsset) => {
    setSelectedPhotos(prev => {
      const isSelected = prev.some(p => p.id === photo.id);
      const newSelected = isSelected 
        ? prev.filter(p => p.id !== photo.id)
        : [...prev, photo];
      
      onPhotoSelect?.(photo);
      return newSelected;
    });
  }, [onPhotoSelect]);

  const handlePhotoClick = useCallback((photo: PhotoAsset) => {
    onPhotoClick?.(photo);
  }, [onPhotoClick]);

  return (
    <PhotoInteraction
      photos={photos}
      selectedPhotos={selectedPhotos}
      onPhotoSelect={handlePhotoSelect}
      onPhotoClick={handlePhotoClick}
      onSelectionChange={onSelectionChange}
      className={className}
    >
      {children}
    </PhotoInteraction>
  );
};