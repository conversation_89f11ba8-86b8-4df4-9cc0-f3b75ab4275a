import * as React from "react";
import { cn } from "@/lib/utils";

const LoadingSpinner = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "animate-spin rounded-full border-2 border-b-transparent",
      className
    )}
    {...props}
  />
));
LoadingSpinner.displayName = "LoadingSpinner";

interface LoadingProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: "sm" | "md" | "lg";
  text?: string;
}

const Loading = React.forwardRef<HTMLDivElement, LoadingProps>(
  ({ className, size = "md", text, ...props }, ref) => {
    const sizeClasses = {
      sm: "h-4 w-4",
      md: "h-6 w-6",
      lg: "h-8 w-8",
    };

    return (
      <div
        ref={ref}
        className={cn("flex items-center justify-center space-x-2", className)}
        {...props}
      >
        <LoadingSpinner className={sizeClasses[size]} />
        {text && (
          <span className="text-sm text-muted-foreground">{text}</span>
        )}
      </div>
    );
  }
);
Loading.displayName = "Loading";

export { Loading, LoadingSpinner };