{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["Bash(cargo install tauri-cli --version \"^2.0\")", "<PERSON><PERSON>(cargo tauri init --help)", "Bash(cargo tauri init --ci -A \"照片库管理器\" -W \"照片库管理器\" -D \"../dist\" -P \"http://localhost:3000\" --before-dev-command \"npm run dev\" --before-build-command \"npm run build\")", "Bash(cargo check)"], "deny": []}}