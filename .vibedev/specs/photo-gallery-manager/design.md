# 设计阶段

## 架构设计

### 整体架构

本应用程序采用分层架构设计，结合 Rust Tauri v2 框架和 Swift FFI 技术，实现高效的系统相册管理和展示。

```mermaid
graph TB
    subgraph "前端层 (Frontend)"
        UI[界面层 - HTML/CSS/JavaScript]
        Layout[布局管理组件]
        Interaction[用户交互处理]
    end
    
    subgraph "后端层 (Backend)"
        Tauri[Tauri v2 核心层]
        Router[请求路由器]
        State[状态管理]
    end
    
    subgraph "系统访问层 (System Access)"
        FFI[Rust FFI 层]
        Swift[Swift 相册访问]
        Photos[macOS Photos 框架]
    end
    
    subgraph "数据处理层 (Data Processing)"
        Cache[缓存管理]
        Thumbnail[缩略图生成]
        ImageProcessing[图像处理]
    end
    
    UI --> Tauri
    Tauri --> Router
    Router --> FFI
    FFI --> Swift
    Swift --> Photos
    Router --> Cache
    Cache --> Thumbnail
    Thumbnail --> ImageProcessing
```

### 技术架构

#### 1. Rust Tauri v2 架构
- **主进程**: 使用 Rust 编写的 Tauri 后端
- **渲染进程**: 基于 Web 技术的前端界面
- **IPC 通信**: 通过 Tauri 的消息传递机制进行进程间通信
- **插件系统**: 利用 Tauri 插件扩展功能

#### 2. Swift + Rust FFI 架构
- **Swift 层**: 负责与 macOS Photos 框架的直接交互
- **Rust FFI 层**: 提供安全的 Rust-Swift 接口绑定
- **桥接层**: 处理数据类型转换和内存管理

## 组件设计

### 1. 系统相册访问组件

#### Swift 组件设计
```swift
// Swift 相册管理器
class PhotoGalleryManager {
    // 权限管理
    func requestPermission() -> Bool
    
    // 相册访问
    func fetchPhotoAssets() -> [PhotoAsset]
    
    // 缩略图生成
    func generateThumbnail(for asset: PhotoAsset, size: CGSize) -> UIImage?
    
    // 相片信息获取
    func getPhotoInfo(for asset: PhotoAsset) -> PhotoInfo
}

// 相片数据模型
struct PhotoAsset {
    let identifier: String
    let creationDate: Date
    let modificationDate: Date
    let mediaType: MediaType
    let location: CLLocation?
}

// 相片信息模型
struct PhotoInfo {
    let fileName: String
    let fileSize: Int64
    let dimensions: CGSize
    let format: ImageFormat
    let creationDate: Date
}
```

#### Rust FFI 绑定设计
```rust
// Rust FFI 绑定
#[repr(C)]
pub struct PhotoAsset {
    pub identifier: *const c_char,
    pub creation_date: i64,
    pub modification_date: i64,
    pub media_type: MediaType,
    pub has_location: bool,
    pub latitude: f64,
    pub longitude: f64,
}

#[repr(C)]
pub struct PhotoInfo {
    pub file_name: *const c_char,
    pub file_size: i64,
    pub width: u32,
    pub height: u32,
    pub format: ImageFormat,
    pub creation_date: i64,
}

// FFI 函数声明
extern "C" {
    pub fn request_photo_gallery_permission() -> bool;
    pub fn fetch_photo_assets() -> *const PhotoAssetArray;
    pub fn generate_thumbnail(
        asset_id: *const c_char,
        width: u32,
        height: u32
    ) -> *const ThumbnailData;
    pub fn free_photo_asset_array(array: *const PhotoAssetArray);
    pub fn free_thumbnail_data(data: *const ThumbnailData);
}
```

### 2. 缓存管理组件

#### 缓存策略设计
```rust
// 缓存管理器
pub struct CacheManager {
    // 内存缓存
    memory_cache: LruCache<String, ThumbnailData>,
    // 磁盘缓存
    disk_cache: DiskCache,
    // 缓存配置
    config: CacheConfig,
}

impl CacheManager {
    // 获取缩略图
    pub async fn get_thumbnail(
        &mut self,
        asset_id: &str,
        size: (u32, u32)
    ) -> Result<ThumbnailData, CacheError> {
        // 1. 检查内存缓存
        if let Some(data) = self.memory_cache.get(asset_id) {
            return Ok(data.clone());
        }
        
        // 2. 检查磁盘缓存
        if let Ok(data) = self.disk_cache.get(asset_id, size) {
            self.memory_cache.put(asset_id.to_string(), data.clone());
            return Ok(data);
        }
        
        // 3. 生成新缩略图
        let data = self.generate_new_thumbnail(asset_id, size).await?;
        self.memory_cache.put(asset_id.to_string(), data.clone());
        self.disk_cache.put(asset_id, size, &data)?;
        
        Ok(data)
    }
}
```

### 3. 界面布局组件

#### React 组件设计
```typescript
// 主相册组件
interface PhotoGalleryProps {
  photos: PhotoAsset[];
  photosPerRow: number;
  onPhotoSelect: (photo: PhotoAsset) => void;
  onPhotoClick: (photo: PhotoAsset) => void;
}

const PhotoGallery: React.FC<PhotoGalleryProps> = ({
  photos,
  photosPerRow,
  onPhotoSelect,
  onPhotoClick
}) => {
  return (
    <div className="photo-gallery">
      <div 
        className="photo-grid"
        style={{
          display: 'grid',
          gridTemplateColumns: `repeat(${photosPerRow}, 1fr)`,
          gap: '8px'
        }}
      >
        {photos.map(photo => (
          <PhotoThumbnail
            key={photo.id}
            photo={photo}
            onSelect={onPhotoSelect}
            onClick={onPhotoClick}
          />
        ))}
      </div>
    </div>
  );
};

// 缩略图组件
interface PhotoThumbnailProps {
  photo: PhotoAsset;
  onSelect: (photo: PhotoAsset) => void;
  onClick: (photo: PhotoAsset) => void;
}

const PhotoThumbnail: React.FC<PhotoThumbnailProps> = ({
  photo,
  onSelect,
  onClick
}) => {
  const [thumbnail, setThumbnail] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // 加载缩略图
    const loadThumbnail = async () => {
      const thumbnailData = await window.tauri.invoke('get_thumbnail', {
        assetId: photo.id,
        width: 200,
        height: 200
      });
      setThumbnail(thumbnailData);
      setLoading(false);
    };

    loadThumbnail();
  }, [photo.id]);

  if (loading) {
    return <div className="thumbnail-loading" />;
  }

  return (
    <div 
      className="photo-thumbnail"
      onClick={() => onClick(photo)}
    >
      <img 
        src={thumbnail || ''} 
        alt={photo.fileName}
        className="thumbnail-image"
      />
      <div className="thumbnail-overlay">
        <input 
          type="checkbox"
          onChange={(e) => {
            e.stopPropagation();
            onSelect(photo);
          }}
        />
      </div>
    </div>
  );
};
```

### 4. 状态管理组件

#### Tauri 命令设计
```rust
// Tauri 命令定义
#[tauri::command]
pub async fn get_photo_assets() -> Result<Vec<PhotoAsset>, String> {
    // 调用 FFI 获取相片列表
    let assets = unsafe { fetch_photo_assets() };
    
    // 转换数据格式
    let photo_assets = convert_photo_assets(assets);
    
    // 释放内存
    unsafe { free_photo_asset_array(assets) };
    
    Ok(photo_assets)
}

#[tauri::command]
pub async fn get_thumbnail(
    asset_id: String,
    width: u32,
    height: u32
) -> Result<String, String> {
    // 检查缓存
    if let Some(cached) = cache_manager.get(&asset_id, (width, height)).await {
        return Ok(cached);
    }
    
    // 生成缩略图
    let thumbnail_data = unsafe {
        generate_thumbnail(
            asset_id.as_ptr(),
            width,
            height
        )
    };
    
    // 转换为 base64 字符串
    let base64_data = convert_to_base64(thumbnail_data);
    
    // 释放内存
    unsafe { free_thumbnail_data(thumbnail_data) };
    
    Ok(base64_data)
}

#[tauri::command]
pub async fn request_permission() -> Result<bool, String> {
    let permission_granted = unsafe { request_photo_gallery_permission() };
    Ok(permission_granted)
}
```

## 数据流设计

### 1. 相片访问数据流
```mermaid
sequenceDiagram
    participant UI as 前端界面
    participant Tauri as Tauri 后端
    participant FFI as Rust FFI
    participant Swift as Swift 层
    participant Photos as macOS Photos

    UI->>Tauri: 请求相片列表
    Tauri->>FFI: 调用 fetch_photo_assets()
    FFI->>Swift: 调用 Swift 函数
    Swift->>Photos: 请求相片数据
    Photos-->>Swift: 返回相片资产
    Swift-->>FFI: 返回相片数组
    FFI-->>Tauri: 转换数据格式
    Tauri-->>UI: 返回相片列表
```

### 2. 缩略图生成数据流
```mermaid
sequenceDiagram
    participant UI as 前端界面
    participant Cache as 缓存管理
    participant FFI as Rust FFI
    participant Swift as Swift 层
    participant Photos as macOS Photos

    UI->>Cache: 请求缩略图
    alt 缓存命中
        Cache-->>UI: 返回缓存数据
    else 缓存未命中
        Cache->>FFI: 请求生成缩略图
        FFI->>Swift: 调用生成函数
        Swift->>Photos: 请求图像数据
        Photos-->>Swift: 返回图像
        Swift-->>Swift: 生成缩略图
        Swift-->>FFI: 返回缩略图数据
        FFI-->>Cache: 转换并缓存
        Cache-->>UI: 返回缩略图数据
    end
```

### 3. 用户交互数据流
```mermaid
sequenceDiagram
    participant User as 用户
    participant UI as 前端界面
    participant State as 状态管理
    participant Backend as 后端服务

    User->>UI: 点击相片
    UI->>State: 更新选中状态
    UI->>Backend: 请求大图
    Backend-->>UI: 返回大图数据
    UI->>User: 显示大图预览
```

## 接口设计

### 1. Rust FFI 接口
```rust
// FFI 接口定义
pub trait PhotoGalleryFFI {
    fn request_permission() -> Result<bool, FFIError>;
    fn fetch_photo_assets() -> Result<Vec<PhotoAsset>, FFIError>;
    fn generate_thumbnail(
        asset_id: &str,
        width: u32,
        height: u32
    ) -> Result<ThumbnailData, FFIError>;
    fn get_photo_info(asset_id: &str) -> Result<PhotoInfo, FFIError>;
}

// 错误类型定义
#[derive(Debug)]
pub enum FFIError {
    PermissionDenied,
    AssetNotFound,
    InvalidFormat,
    MemoryError,
    UnknownError,
}
```

### 2. Tauri 命令接口
```rust
// Tauri 命令接口
#[tauri::command]
pub async fn get_photo_assets(
    state: State<'_, AppState>
) -> Result<Vec<PhotoAsset>, String> {
    state.gallery_manager.get_photo_assets().await
}

#[tauri::command]
pub async fn get_thumbnail(
    asset_id: String,
    width: u32,
    height: u32,
    state: State<'_, AppState>
) -> Result<String, String> {
    state.cache_manager.get_thumbnail(&asset_id, (width, height)).await
}

#[tauri::command]
pub async fn search_photos(
    query: String,
    state: State<'_, AppState>
) -> Result<Vec<PhotoAsset>, String> {
    state.gallery_manager.search_photos(&query).await
}
```

### 3. 前端接口
```typescript
// 前端服务接口
interface PhotoGalleryService {
  // 权限管理
  requestPermission(): Promise<boolean>;
  
  // 相片获取
  getPhotoAssets(): Promise<PhotoAsset[]>;
  getPhotoInfo(assetId: string): Promise<PhotoInfo>;
  
  // 缩略图获取
  getThumbnail(assetId: string, width: number, height: number): Promise<string>;
  
  // 搜索功能
  searchPhotos(query: string): Promise<PhotoAsset[]>;
}

// 数据模型
interface PhotoAsset {
  id: string;
  fileName: string;
  fileSize: number;
  width: number;
  height: number;
  format: string;
  creationDate: Date;
  thumbnail?: string;
}

interface PhotoInfo {
  fileName: string;
  fileSize: number;
  dimensions: { width: number; height: number };
  format: string;
  creationDate: Date;
  location?: { latitude: number; longitude: number };
}
```

## 性能优化设计

### 1. 缓存策略
- **多级缓存**: 内存缓存 + 磁盘缓存
- **LRU 算法**: 最近最少使用算法管理内存缓存
- **预加载策略**: 预加载即将显示的缩略图
- **缓存清理**: 定期清理过期缓存文件

### 2. 内存管理
- **RAII 模式**: 使用 Rust 的所有权系统管理内存
- **弱引用**: 对于大图数据使用弱引用避免内存泄漏
- **延迟加载**: 按需加载图像数据
- **内存池**: 预分配内存池减少频繁分配

### 3. 并发处理
- **异步编程**: 使用 async/await 处理 I/O 操作
- **线程池**: 使用线程池处理并发图像处理任务
- **批处理**: 批量处理相片数据减少系统调用
- **优先级队列**: 高优先级任务优先处理

## 错误处理设计

### 1. 错误类型定义
```rust
// 应用错误类型
#[derive(Debug)]
pub enum AppError {
    // 权限错误
    PermissionDenied(String),
    // 系统错误
    SystemError(String),
    // 网络错误
    NetworkError(String),
    // 数据错误
    DataError(String),
    // 用户错误
    UserError(String),
}

impl Display for AppError {
    fn fmt(&self, f: &mut Formatter<'_>) -> Result<(), Error> {
        match self {
            AppError::PermissionDenied(msg) => write!(f, "权限拒绝: {}", msg),
            AppError::SystemError(msg) => write!(f, "系统错误: {}", msg),
            AppError::NetworkError(msg) => write!(f, "网络错误: {}", msg),
            AppError::DataError(msg) => write!(f, "数据错误: {}", msg),
            AppError::UserError(msg) => write!(f, "用户错误: {}", msg),
        }
    }
}
```

### 2. 错误恢复策略
- **重试机制**: 对于临时性错误自动重试
- **降级服务**: 在部分功能失效时提供基本功能
- **用户提示**: 提供清晰的错误信息和解决方案
- **日志记录**: 详细记录错误信息用于调试

## 安全性设计

### 1. 权限管理
- **最小权限原则**: 只请求必要的权限
- **权限检查**: 在每次访问前检查权限状态
- **权限恢复**: 提供权限重新请求机制
- **权限状态监控**: 实时监控权限变化

### 2. 数据安全
- **本地存储**: 所有数据仅存储在本地设备
- **加密传输**: 使用加密协议传输敏感数据
- **数据验证**: 对所有输入数据进行验证
- **安全日志**: 记录关键操作的安全日志

### 3. 隐私保护
- **无数据上传**: 确保用户相片不上传到服务器
- **匿名处理**: 不收集用户身份信息
- **本地处理**: 所有处理都在本地完成
- **用户控制**: 用户完全控制自己的数据

## 测试策略

### 1. 单元测试
- **FFI 接口测试**: 测试 Rust-Swift 绑定
- **缓存管理测试**: 测试缓存功能
- **数据处理测试**: 测试图像处理功能
- **错误处理测试**: 测试各种错误情况

### 2. 集成测试
- **端到端测试**: 测试完整的用户流程
- **性能测试**: 测试应用性能指标
- **兼容性测试**: 测试不同 macOS 版本兼容性
- **权限测试**: 测试权限管理功能

### 3. 用户验收测试
- **功能验收**: 验证所有功能需求
- **性能验收**: 验证性能指标
- **安全验收**: 验证安全要求
- **用户体验验收**: 验证用户体验

## 部署设计

### 1. 构建配置
- **发布版本**: 优化后的发布构建
- **开发版本**: 包含调试信息的开发构建
- **测试版本**: 用于测试的特殊构建
- **签名验证**: 代码签名和公证

### 2. 安装包设计
- **DMG 安装包**: macOS 标准安装包
- **拖拽安装**: 简单的拖拽安装方式
- **自动更新**: 内置更新检查机制
- **卸载支持**: 提供完整的卸载功能

### 3. 版本管理
- **语义化版本**: 使用语义化版本号
- **更新策略**: 定义更新策略和流程
- **回滚机制**: 支持版本回滚
- **兼容性保证**: 保证向后兼容性

## 总结

本设计文档提供了 photo-gallery-manager 功能的完整技术方案，包括：

1. **架构设计**: 清晰的分层架构和技术栈选择
2. **组件设计**: 各个功能模块的详细设计
3. **数据流设计**: 数据在系统中的流动过程
4. **接口设计**: 各层之间的接口定义
5. **性能优化**: 确保应用的高性能运行
6. **错误处理**: 完善的错误处理机制
7. **安全性设计**: 全面的安全考虑
8. **测试策略**: 多层次的测试保障
9. **部署设计**: 完整的部署和分发方案

该设计满足了所有需求规格中的功能和非功能需求，为后续的开发实现提供了清晰的技术指导。